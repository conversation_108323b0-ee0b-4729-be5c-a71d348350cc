.tray-kit {
    border: 1px solid #e1e1e1;
    margin-bottom: 15px;
    padding: 15px;
    
    
    div[style="clear:both;"] {
        display: none;
    }
}

.tray-kit-item {
    border-top: 1px solid #e1e1e1;
    padding: 15px 0 25px;
    
    &:first-child {
        border-top: none;
    }

    &:after {
        clear: both;
        content: '';
        display: block;
    }
    
    > div {
        position: relative;
    }
}

.tray-kit-image {
    float: left;
    width: 90px;
    margin-right: 15px;
    border: 1px solid #e1e1e1;
}

.tray-kit-name {
    font-weight: 700;
    padding: 5px 0;
}

.tray-kit-info {
    overflow: hidden;
    margin-left: 105px;
}

.tray-kit-unity {
    overflow: hidden;
    margin-left: 0;
    position: absolute;
    left: 0;
    top: 95px;
    width: 90px;
    text-align: center;
}

.tray-kit-show-all {
    font-size: 1.2rem;
    text-align: center;
    margin-top: 10px;
}

.tray-kit-title h3 {
    font-weight: 700;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.tray-kit-additional-info .onVar,
.tray-kit-additional-info .onVar:hover{
    color: #000;
    font-size: 1.2rem;
}

.tray-kit-additional-info {
    
    .select,
    .text,
    .textarea,
    .table,
    .dd,
    .ddTitle{
        display: block;
        width: 100% !important;
    }
    
    .text,
    .textarea{
        margin-bottom: 5px !important;
    }
    
    .dd .ddTitle span.ddTitleText{
        font-weight: 400;
        font-size: 1.2rem;
    }
    
    .select,
    .ddTitle,
    .textarea{
        border: 1px solid #e1e1e1;
        font-weight: 400;
        font-size: 1.2rem;
        height: 24px;
    }
    
    .varCont > *{
        color: #222;
        display: block;
    }
}

.tray-kit-variation{
    
    img {
        display: block;
        height: 30px;
        overflow: hidden;
        width: 30px;
    }
    
    li {
        border: 1px solid #e1e1e1;
        display: inline-block;
        font-size: 1.4rem;
        line-height: 30px;
        margin: 0 5px 5px 0;
        min-height: 30px;
        min-width: 30px;
        text-align: center;
    }
}