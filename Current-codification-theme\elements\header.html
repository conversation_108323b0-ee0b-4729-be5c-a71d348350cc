<header class="header-mobile">

    <div class="header-menu" data-toggle="overlay-shadow" data-target="#menu-mobile">
        <div></div>
        <div></div>
        <div></div>
    </div>

    {% element 'snippets/header-search' %}

    <a class="cart-toggle" href="{{ links.cart }}" title="Carrinho">
        <i class="icon icon-shopping-cart v-align-middle"></i>
        <span class="cart-quantity" data-cart="amount"></span>
    </a>

</header>

<header class="header">

    <div class="bg">

        <div class="line flex align-center justify-between container">

            <div class="header-menu" data-toggle="overlay-shadow" data-target="#menu-mobile">
                <div></div>
                <div></div>
                <div></div>
            </div>
            {% set logoStore = Image('logo_loja') %}
            <a class="logo" href="{{ store.url }}" title="{{ store.name }}">

                {% if logoStore %}
                    {{ logoStore | replace({'src=': 'width="222" height="36" src='}) }}
                {% endif %}

                {% if 'home' in pages.current %}
                    <h1 class="title-store">{{ store.name }}</h1>
                {% else %}
                    <div class="title-store">{{ store.name }}</div>
                {% endif %}

            </a> 

            {% element 'snippets/header-search' %}

            <div class="account flex align-center">

                <i class="account-icon icon icon-login-new"></i>

                <div class="tray-hide" data-logged-user="true">
                    <span>Ol&aacute;, <span data-customer="name"></span></span>
                    <div class="login-links">
                        <a href="{{ links.central }}" title="Minha Conta">Minha conta</a>
                        <span>/</span>
                        <a href="{{ links.logout }}" title="Sair">Sair</a>
                    </div>
                </div>  
                <div data-logged-user="false">
                    <span>Minha Conta</span>
                    <div class="login-links">
                        <a href="{{ links.login }}" title="Entrar">Entrar</a>
                        <span>/</span>
                        <a href="{{ links.register }}" title="Cadastrar">Cadastrar</a>
                    </div>
                </div> 

                

            </div>

            <a class="cart-toggle" href="{{ links.cart }}" title="Carrinho">
                <i class="icon icon-shopping-cart v-align-middle"></i>
                <span class="cart-quantity" data-cart="amount">0</span>
            </a>

        </div>
    </div>

    {% element 'snippets/menu' %}

</header>