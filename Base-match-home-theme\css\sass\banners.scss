/**
 * Banners
 */

.banner{
    border: 2px solid #e2e2e2;
}

.banner-ruler{
    background: #fff;
    margin-bottom: 30px;
    width: 849px;
    @media screen and (max-width: 1200px){ 
        width: auto;
    }

}

.banner-sidebar{
    margin-top: 30px;
}

.banner-js {
    margin: 0;
    padding-bottom: 0;
    
    .box_skitter_large{
    	left: 50% !important;
    	-webkit-transform: translateX(-50%);
    	   -moz-transform: translateX(-50%);
    	    -ms-transform: translateX(-50%);
    	     -o-transform: translateX(-50%);
        		transform: translateX(-50%);
    }
    
    .box_skitter .info_slide_dots{
    	bottom: 0;
    	padding: 20px;
    }

}

.banner-home{
    margin-bottom: 30px;
    position: relative;
    z-index: 10;
    
    .prev_button, 
    .next_button {
        display: none!important; 
    }
    
    .info_slide {
        top: 85%;
        width: 98%;
        text-align: center;
        background: none;
        
        .image_number {
            float: none!important;
            font-size: 0;
            width: 15px;
            height: 15px;
            display: inline-block;
            border-radius: 0;
        }
    }
    
    .slick-slide{
        img{ margin: 0 auto; }
    }

    .slick-arrow{
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .arrow-next{
        right: 15px;
    }

    .arrow-prev{
        left: 15px;
    }

    .slick-dots{
        bottom: 15px;
        left: 0;
        margin-top: 5px;
        position: absolute;
        text-align: center;
        width: 100%;

        li{
            display: inline-block;
            margin: 0 3px;
        }

        button{
            border-radius: 15px;
            border: none;
            font-size: 0;
            height: 15px;
            width: 15px;
        }
    }
}

.banner-bottom{ margin-top: 15px; }

.mobile-off{
    @media screen and (max-width: $sm){ display: none; }
}