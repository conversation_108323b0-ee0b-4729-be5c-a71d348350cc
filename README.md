# Decodificador de Entidades HTML

Este projeto contém ferramentas Python para decodificar entidades HTML em arquivos de texto.

## Problema

Arquivos HTML frequentemente contêm entidades HTML codificadas como:
- `&ccedil;` → ç
- `&atilde;` → ã  
- `&aacute;` → á
- `&eacute;` → é
- `&iacute;` → í
- `&ocirc;` → ô
- `&uacute;` → ú
- `&ntilde;` → ñ
- `&amp;` → &
- `&lt;` → <
- `&gt;` → >
- `&quot;` → "

## Ferramentas Disponíveis

### 1. `decodificar_simples.py`
Script simples que decodifica o arquivo `settings.html` automaticamente.

**Uso:**
```bash
python decodificar_simples.py
```

**O que faz:**
- Lê o arquivo `settings.html`
- Detecta automaticamente a codificação do arquivo
- Decodifica todas as entidades HTML
- Salva o resultado em `settings_decodificado.html`
- Mostra estatísticas do processo

### 2. `decodificar_html.py`
Script completo com mais opções e flexibilidade.

**Uso básico:**
```bash
# Sobrescreve o arquivo original
python decodificar_html.py arquivo.html

# Cria um novo arquivo
python decodificar_html.py arquivo.html arquivo_decodificado.html

# Modo interativo
python decodificar_html.py --interativo

# Ver ajuda
python decodificar_html.py --help
```

### 3. `decodificar_iso.py`
Script específico para manter a codificação ISO-8859-1.

**Uso:**
```bash
python decodificar_iso.py
```

**O que faz:**
- Lê o arquivo original mantendo sua codificação nativa (ISO-8859-1)
- Decodifica entidades HTML
- Salva o resultado mantendo a codificação ISO-8859-1
- Cria o arquivo `settings_final.html`

### 4. `verificar_resultado.py`
Script para verificar se a decodificação foi bem-sucedida.

**Uso:**
```bash
python verificar_resultado.py
```

**Exemplos:**
```bash
# Decodificar settings.html e criar settings_novo.html
python decodificar_html.py settings.html settings_novo.html

# Decodificar e sobrescrever o arquivo original
python decodificar_html.py settings.html

# Modo interativo para testar textos
python decodificar_html.py -i
```

## Instalação

1. Certifique-se de ter Python 3.6+ instalado
2. Instale a dependência necessária:
```bash
pip install chardet
```

## Recursos

- **Detecção automática de codificação**: Os scripts detectam automaticamente se o arquivo está em UTF-8, ISO-8859-1, ou outra codificação
- **Preservação da estrutura**: O arquivo decodificado mantém toda a estrutura original, apenas convertendo as entidades HTML
- **Estatísticas**: Mostra quantas entidades foram encontradas e decodificadas
- **Segurança**: Cria backup automático (não sobrescreve sem confirmação)

## Exemplo de Conversão

**Antes:**
```html
<div class="title">Configura&ccedil;&otilde;es</div>
<span>T&iacute;tulo</span>
<p>voc&ecirc; n&atilde;o</p>
<div>Aten&ccedil;&atilde;o</div>
<section>Se&ccedil;&atilde;o</section>
```

**Depois:**
```html
<div class="title">Configurações</div>
<span>Título</span>
<p>você não</p>
<div>Atenção</div>
<section>Seção</section>
```

## Arquivos Gerados

- `settings_decodificado.html` - Resultado do script simples
- `settings_novo.html` - Exemplo de arquivo gerado pelo script completo
- `settings_final.html` - **Arquivo final em ISO-8859-1** (recomendado para uso)
- `settings_iso.html` - Arquivo corrigido para ISO-8859-1

## Notas Técnicas

- Os scripts usam a biblioteca `html.unescape()` do Python para decodificação
- A biblioteca `chardet` é usada para detectar automaticamente a codificação dos arquivos
- Suporta arquivos em diferentes codificações (UTF-8, ISO-8859-1, Windows-1252, etc.)
- Preserva a formatação e estrutura original dos arquivos

## Solução de Problemas

**Erro de codificação:**
- Os scripts detectam automaticamente a codificação, mas se houver problemas, verifique se o arquivo não está corrompido

**Arquivo não encontrado:**
- Certifique-se de que o arquivo está no mesmo diretório dos scripts
- Verifique se o nome do arquivo está correto

**Permissões:**
- Certifique-se de ter permissão de leitura no arquivo de entrada
- Certifique-se de ter permissão de escrita no diretório de saída

## Contribuição

Sinta-se à vontade para melhorar estes scripts ou reportar problemas!
