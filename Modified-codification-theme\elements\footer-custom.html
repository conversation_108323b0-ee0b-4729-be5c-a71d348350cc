<!-- <PERSON><PERSON><PERSON> -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="footer-content">
                    
                    <!-- Newsletter -->
                    {% if settings.newsletter_active %}
                    <div class="newsletter-section">
                        <div class="newsletter-content">
                            <h3>{{ settings.newsletter_title | default('Receba nossas ofertas') }}</h3>
                            <p>{{ settings.newsletter_description | default('Cadastre-se e receba promoções exclusivas') }}</p>
                            <form class="newsletter-form">
                                <input type="email" placeholder="Seu e-mail" required>
                                <button type="submit">Cadastrar</button>
                            </form>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Menus Customizáveis -->
                    <div class="footer-menus">
                        {% set hasCustomMenus = false %}
                        
                        <!-- Verificar se há menus customizados -->
                        {% for i in 1..10 %}
                            {% set column_title = attribute(settings, 'footer_column_title_' ~ i) %}
                            {% if column_title %}
                                {% set hasCustomMenus = true %}
                            {% endif %}
                        {% endfor %}
                        
                        {% if hasCustomMenus %}
                            <!-- Menus Customizados -->
                            {% for i in 1..10 %}
                                {% set column_title = attribute(settings, 'footer_column_title_' ~ i) %}
                                {% if column_title %}
                                    <div class="box box-custom-menu">
                                        <div class="title">{{ column_title }} <i class="icon icon-arrow-down"></i></div>
                                        <div class="overflow">
                                            <ul class="list">
                                                {% for j in 1..20 %}
                                                    {% set link_title = attribute(settings, 'footer_link_title_' ~ i ~ '_' ~ j) %}
                                                    {% set link_url = attribute(settings, 'footer_link_url_' ~ i ~ '_' ~ j) %}
                                                    {% set link_blank = attribute(settings, 'footer_link_blank_' ~ i ~ '_' ~ j) %}
                                                    
                                                    {% if link_title and link_url %}
                                                        <li>
                                                            <a href="{{ link_url }}" title="{{ link_title }}"{% if link_blank %} target="_blank" rel="noopener"{% endif %}>
                                                                {{ link_title }}
                                                            </a>
                                                        </li>
                                                    {% endif %}
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <!-- Menus Padrão (fallback) -->
                            <div class="box box-categories">
                                <div class="title">Categorias <i class="icon icon-arrow-down"></i></div>
                                <div class="overflow">
                                    <ul class="list">
                                        {% for category in categories %}
                                            <li>
                                                <a href="{{ category.link }}" title="{{ category.name }}">
                                                    {{ category.name }}
                                                </a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="box box-pages">
                                <div class="title">Institucional <i class="icon icon-arrow-down"></i></div>
                                <div class="overflow">
                                    <ul class="list">
                                        {% for custom in pages.custom %}
                                        <li>
                                            <a href="{{ custom.url }}" title="{{ custom.name }}">{{ custom.name }}</a>
                                        </li>
                                        {% endfor %}
                                        <li>
                                            <a href="{{ links.contact }}" title="Fale Conosco">Fale Conosco</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="box box-infos">
                                <div class="title">Atendimento <i class="icon icon-arrow-down"></i></div>
                                <div class="overflow">
                                    <ul class="list"> 
                                        {% if whatsappNumber %}
                                        <li>
                                            {% element 'snippets/whatsapp' { 'number': whatsappNumber, 'include_text' : false } %}
                                        </li>
                                        {% endif %}
                                        
                                        {% if phone %}
                                        {% set link = phone | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                                        <li>
                                            <a href="tel:{{ link }}" title="Telefone: {{ phone }}">
                                                <i class="icon icon-phone v-align-middle"></i>
                                                {{ phone }}
                                            </a>
                                        </li>
                                        {% endif %}
                                        
                                        {% if email %}
                                        <li>
                                            <a href="mailto:{{ email }}" title="Email: {{ email }}">
                                                <i class="icon icon-email v-align-middle"></i>
                                                {{ email }}
                                            </a>
                                        </li>
                                        {% endif %}
                                        
                                        {% if Translation('ag_atendimento') %}
                                        <li class="hour">
                                            <div class="flex">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                                    <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z"></path>
                                                </svg>
                                                <span>Horário de Atendimento:</span>
                                            </div>
                                            <div class="text">
                                                <span>{{ Translation('ag_atendimento') }}</span>
                                            </div>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Informações da Empresa -->
                    <div class="footer-info">
                        <div class="company-info">
                            {% if store.name %}
                                <h4>{{ store.name }}</h4>
                            {% endif %}
                            {% if store.address %}
                                <p>{{ store.address }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Redes Sociais -->
                        {% if settings.social_facebook or settings.social_instagram or settings.social_twitter %}
                        <div class="social-media">
                            <h4>Siga-nos</h4>
                            <div class="social-links">
                                {% if settings.social_facebook %}
                                    <a href="{{ settings.social_facebook }}" target="_blank" rel="noopener" title="Facebook">
                                        <i class="icon icon-facebook"></i>
                                    </a>
                                {% endif %}
                                {% if settings.social_instagram %}
                                    <a href="{{ settings.social_instagram }}" target="_blank" rel="noopener" title="Instagram">
                                        <i class="icon icon-instagram"></i>
                                    </a>
                                {% endif %}
                                {% if settings.social_twitter %}
                                    <a href="{{ settings.social_twitter }}" target="_blank" rel="noopener" title="Twitter">
                                        <i class="icon icon-twitter"></i>
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Copyright -->
                    <div class="footer-bottom">
                        <div class="copyright">
                            <p>&copy; {{ "now"|date("Y") }} {{ store.name }}. Todos os direitos reservados.</p>
                        </div>
                        
                        <!-- Selos de Segurança -->
                        {% if settings.security_seals %}
                        <div class="security-seals">
                            {{ settings.security_seals | raw }}
                        </div>
                        {% endif %}
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</footer>
