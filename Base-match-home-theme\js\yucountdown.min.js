!function(t,e,n){t.fn.yuukCountDown=function(e){var n={starttime:"",endtime:"",startCallBack:t.noop,notStartCallBack:t.noop,endCallBack:t.noop},a=t.extend(n,e);return this.each(function(e,n){({timer:null,countDown:function(){var t=Math.round((new Date).getTime()/1e3),e=Math.round(new Date(a.starttime)/1e3);endTime=Math.round(new Date(a.endtime)/1e3);var n=endTime-t;return{endLeftTime:n,startLeftTime:e-t,day:parseInt(n/60/60/24),hour:parseInt(n/60/60%24),minute:parseInt(n/60%60),second:parseInt(n%60)}},setHtml:function(e){var i=e.endLeftTime,l=e.startLeftTime,s=this.fillZero(e.day),r=this.fillZero(e.hour),o=this.fillZero(e.minute),c=this.fillZero(e.second);l>0?a.notStartCallBack&&a.notStartCallBack(e):(i>0&&(t(n).html(['<li class="item fundo-principal"><i class="hour">'+s+"</i><span>Dias</span></li>",'<li class="blank">:</li>','<li class="item fundo-principal"><i class="hour">'+r+"</i><span>Horas</span></li>",'<li class="blank">:</li>','<li class="item fundo-principal"><i class="minute">'+o+"</i><span>Min</span></li>",'<li class="blank">:</li>','<li class="item fundo-principal"><i class="second">'+c+"</i><span>Seg</span></li>"].join(" ")),a.startCallBack&&a.startCallBack(e)),i<=0&&(a.endCallBack&&a.endCallBack(e),clearInterval(this.timer)))},fillZero:function(t){return t<10&&(t="0"+t),t},init:function(){var t=this;if(new Date(a.endtime)<=new Date(a.starttime))throw new Error("O horario de inicio da contagem regressiva nao pode ser maior que o horario de termino");this.timer=setInterval(function(){t.setHtml(t.countDown())},1e3)}}).init()})}}(jQuery,window);