.page-login{

    .carrinho-heading,
    .titulo-login,
    .<PERSON><PERSON><PERSON>{
        display: none;
    }

    .mensagensErro{
        margin: -20px 0 10px;
    }

    .caixa-login{
        background: #fff;
        border: 1px solid #e2e2e2;
        float: left;
        margin-right: 3%;
        min-height: 360px;
        width: 48.5%;

        @media screen and (min-width: 1200px){
            padding: 30px 60px;
        }

        @media screen and (min-width: 768px) and (max-width: 1199px){
            padding: 15px;
        }

        @media screen and (max-width: $sm){
            margin-top: 30px;
            padding: 15px;
        }

        h3{
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
        }
    }

    .caixa-cadastro{
        background: #fff;
        border: 1px solid #e2e2e2;
        float: left;
        min-height: 360px;
        width: 48.5%;

        @media screen and (min-width: 1200px){
            padding: 30px 60px;
        }

        @media screen and (min-width: 768px) and (max-width: 1199px){
            padding: 15px;
        }

        @media screen and (max-width: $sm){
            margin-top: 30px;
            padding: 15px;
        }

        h3{
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            text-align: center;
        }
    }

    .cadastro-campo{
        &.campo-cep{
            margin-bottom: 10px;

            label{
                display: inline-block;
                vertical-align: top;
            }

            input{
                padding: 6px 10px;
                border: 1px solid #e1e1e1;
            }
        }
    }

    label{
        display: block;
        font-size: 1.4rem;
        text-align: left;
    }

    > input[type="text"],
    #email_cadastro,
    #email_login,
    #senha_login{
        border: 1px solid #e1e1e1;
        color: #000;
        display: block;
        margin: 5px 0 30px;
        padding: 6px 10px;
        width: 100% !important;
    }

    fieldset{
        text-align: center;

        br{
            display: none;
        }

        button{
            padding: 10px 30px;
        }
    }

    .cadastro-desc{
        display: none;
    }

    .login-campos-obrigatorios{
        display: none;
    }

    .recuperar-senha{
        margin-top: 30px;

        a{
            color: #3d4445;
            font-size: 1.2rem;
        }
    }

    @media screen and (max-width: 480px){

        .caixa-cadastro {
            margin: 30px 0 0 0;
        }

        .caixa-cadastro,
        .caixa-login{
            display: block;
            float: none;
            padding: 20px;
            width: 100%;
        }

    }
}