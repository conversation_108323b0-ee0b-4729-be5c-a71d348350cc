/* Fonts */
@font-face {
    font-family: 'go';
    src: url('fonts/go/go-v3.eot');
    src: url('fonts/go/go-v3.eot') format('embedded-opentype'),
         url('fonts/go/go-v3.woff2') format('woff2'),
         url('fonts/go/go-v3.woff') format('woff'),
         url('fonts/go/go-v3.ttf') format('truetype'),
         url('fonts/go/go-v3.svg') format('svg');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "icons-new";
    src: url("data:font/woff2;base64,d09GMgABAAAAAAPoAAsAAAAACEAAAAOaAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDDAqEbIQkATYCJAMQCwoABCAFg3IHShspBxGVmxXJfh7YtpJtsjb6DfXMMpZoDY3wkfSe8x916fuSdSCHgXwuEhhCU4mHiXDkuZ2Bt6QNAOJ/bvf2N9mapdi0NEswJEFwdeouHOAGpBdWvOOX5zj5dZR4b+Jy2iT8BWCNOXHMDZYVWBfQmNtLKJHEPA3zLhPbDvCT14AAUp4a7G7pGoLPP3fDKkRQsEqCwp/+pYvnwqWLKQOBxIU4ps76RGcgAPfxBshIvn4GPrqBEQV2QN/bvAAPf+ZnURaAyLQemYTuDCAAHJB1MSk9w+oBPLNk4WSXWpEitVcQ/Kz///1MRL72Xx5AGA60gfl6AYAiWDn8TMBCwmlgAZwCBrS7McUFcAM+Az8xftQ2L2SU3Z2Rm5StePvo8pgB7DFV9bhNtNfyHLu25/rxm3tvaMeCjKBaGdlbPXY82HQsEaMhV3QwjVFMC0wbYtP0yRrzRhfzmsO8DtbNzZvqMtPeG2JZx4ygKgoJ2hNMZnC1OZIZ2sbMWJN4nFGXpNYUum0xqfRcgKQ1hTE8PHL5qj0WohoJQW05NnpF6kOStD2GMU1k28eKtGMZyia2tDZk8+sqFs8eswRWFRCSkPjYOf6L4xbHw/XKdP/3z4569xWX98fMCXsXP7MsbFZ59LLoes9ENG6uE5oId2x26LLQEslIXl8QxMOFRy4S4b5DvjqUlq8GAs4LFwubCw6hcBWivtdDQ4MIFM0vX+S3nUaHxCL3IoGPi3ZHDytDXNUpMDCfX1J9FY+y3ZvdmvAZv/BQXCgb/Ffc3t7RErO2auOibK+wDa760938gmhZt2aRM3WBs78waJufMr80TNu7vsQZVFjPsQbbiS7KAgAqkgBAi0J/iW74Mq/i7Wzfqr9THa7Zcdzbk1CXWV0YTKZu09gJ/P+f7g+g3jmSRAuoPw9nsLM/GECSgP9gWj/Pt9ASMdkXYJxEAXCCF1rB+wOCKgRwEOIBKVPKbFWgfhx1N4i0gADGxyqAk3agFfwhQAhxBnCQrgCSx73JqpQ4+E/R1m5o30lUIoedXlibpw2mfqx+4oobVsGoQf6hfezMbMBUyrDSxpgSQakUUHHvmMsyDKbrp72ivPOKtnZD+06iUlKmfKFLapBQH6Z+HEJf2dswUjDqVeUf2sfOzAYMKSmDvS4jTWQmziSHXbo7BVTcuwWmsgxyOTOV3R/MzfeI9ktlYMP7vqXSM8zkt4g6NJwTWXXspTkqxW+2U6LybgcAAA==") format("woff2");
}

@font-face {
    font-family: "icons-google";
    font-display: swap;
    src: url("data:font/woff2;base64,d09GMgABAAAAAAXYAAsAAAAAC4gAAAWJAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCfgqLWIknATYCJAMMCwgABCAFg3IHORuJCVGULU6F7Gdh7OYYoj7xN9729rdeVCL6KcJ4fcUSPE921ftVXc1F98o9C52ISHQ0l51jSNk5AmPHszCn2VgRqgwlSnRER11KgdIBGXd3Yy/k+d790EvLdy0nKSL5suRtMl/tNd8GxgoILflZbJuvdnj/c7+X/2+M1EgAe1H3R+Ui2z4OcDbAqqbZdGfgE/M9ZHMY2WwqvvIiCODTghH+Pv1WzxCbP+XkQokgwWSBuqXMn6OJENW9WDNiwXXY2E1liRzGgKR4BTTkP36YDIYCCqMJjjFi5liBz9/yW0kTgGLpT9kHFlaAATTQ5MT8Mm4+OoJACU1yqz8WDVIlt7cQ+K1SqZQt+Gn/wwNBgdgE1jTg1VUp+C0xcdMIYEPc9tnABkoBtbgA7HUyj9fJAVWSlhp+keL0qOy4prhITEhFSo5LTUtsisLiksyN+0GNf1N251sWXkF3kxsl7C0KH+QgI7j0E/VrSaHP8IF0TpupiKVWjt95J7wl7qlKc1PflhvTDrLmYgTqoS53VySRoFwZxi7hCjnvA6cJQYEdVq8xgrUz3A++kL8lboHiaG9lWJ9Bl+7iRuDk0tCm5kFYWUEJhhP0KbTJIBCCwgy5nZQOpXYBBYSTewQMHHZJ5FhAeeUkqxgBQmwfyFGIMCJcWhkMkwbcIObASsAUa2aBqR4sQ5FKA4FSQv50qey6CegFuLk+J1fgA09ocQjMiAuUi9mZmE9ZOdGYmqPzlIyRwHcZELDThQbFkk0dyCrbtkQgIbBxgH3uSju5wZy8XX4/+0YBcz6j4lcF2bUyawGu516qvFlqOMN1+g5ciD4bZmzI6FV9Fb5YLUu33y+9/1lX+DVZ173Jfzp5PtWR0qdBhivqy9vJm88pS8/7+D/m5chZydnoy8Sbo/mKIzfd7nwIO8a794v2k1HHMMlWu7D7kutcan2sNNl6KfFSm3lVy6WYZ52j3RcKL+yOO1bA3e2UDtwmFmYmpgvWF//uGCw/4pXXFQT2qda7U/LeFQUTVk9So0PjQ4+JlydT3SF7ixX79KzcKjgzK2mzd9MpKjmXfYPyUttM9jOPL35CYuR/kTfQDGp1MSNV1CwFIlF4NCVb67C11yrKEkckVApBiFRP3cZkh628VqSVo2K1Lze92jnnnhvkD+1QlGsyvMAEGSNMmryCl2ay47oMqyVpq42OZjdZ0tLKnOfZKiKwXXico7RbUCF+53O6E6ebRJht+uoN7voyXAxYwtREfGXYkvvJql5CWlgtotV6ZJ96bHWfUZFRfbKxuX1GBs85h52Z46KbnQmzI7MvHl0O2ZZIObhERS5RINE9lTvhk6ykXBWpxtiO7zmx8zM/z9o/rWjb5tEdOa60MoQ3YUbrwGQZa5dtjcyC9yx+Lg8sW3627udVjqway2H6d7K70pHZ2E//etarbOryQ1SLZIS8lKDex8iKBg84w4XkVmi4/jFEABbVpeIASIYU/Zvm4+7oWb9mdvnvBv+TKayvfqpyFF9dBOB+1WICGJ+umRnoz0N+9zAJx1jEKMh3ir+YPfHxgRRBPORfPC4YdHNQOJgadGlPA5bFixCIB+QB2Lo9Br1PI43NA7J5IIBYHoLPdKZXZLBQr/FZz7J4O/WGPN70Nj5n9T7j+Fg/oEbsOdPYPT6SdrbFA8P90CsdikbsYlOzFtZnTMmlg3giBTfX4PFKrXuyXMXdpezg+CL3RLbNeLCUTZJPzf3lJ6/X0Hzm2J82txY5YCDDuBx5hNA4YyNyYH3K8CvkYj0Mpkc6NGnPsaZWd95c5AShhCXuafsjFzS6nMKwCsadDzAOyEQMQ4ItAztWb7zchJDlaBNdoNyjRyucSMVam8R25xWi/o+DBHvWokSLhT1rEWs2hZfNNshWAAAAAAA=") format("woff2");
}

.icon {
    display: inline-block;
    font: normal normal normal 1em/1 'go';
    speak: none;
    text-transform: none;
    /* Better Font Rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"].v-align-middle,
[class*=" icon-"].v-align-middle{
    vertical-align: middle;
}

.icon-linkedin:before {
    content: "\f101";
    font-family: "icons-new";
}
.icon-login-new:before {
    content: "\f102";
    font-family: "icons-new";
    display: block;
}
.icon-shopping-cart:before {
    content: "\f103";
    font-family: "icons-new";
    display: block;
}


.icon-google:before {
    content: "\f101";
    font-family: "icons-google";
}

.icon-shield:before {
    content: "\f102";
    font-family: "icons-google";
}


.icon-arrow-down::before {
  content: "\ea01";
}

.icon-arrow-left::before {
  content: "\ea02";
}

.icon-arrow-right::before {
  content: "\ea03";
}

.icon-card::before {
  content: "\ea04";
}

.icon-cart::before {
  content: "\ea05";
}

.icon-discount::before {
  content: "\ea06";
}

.icon-email::before {
  content: "\ea07";
}

.icon-equal::before {
  content: "\ea08";
}

.icon-facebook::before {
  content: "\ea09";
}

.icon-favorite::before {
  content: "\ea0a";
}

.icon-filter::before {
  content: "\ea0b";
}

.icon-instagram::before {
  content: "\ea0c";
}

.icon-login::before {
  content: "\ea0d";
}

.icon-phone::before {
  content: "\ea0e";
}

.icon-pinterest::before {
  content: "\ea0f";
}

.icon-plus::before {
  content: "\ea10";
}

.icon-reviews::before {
  content: "\ea11";
}

.icon-reward::before {
  content: "\ea12";
}

.icon-search::before {
  content: "\ea13";
}

.icon-security::before {
  content: "\ea14";
}

.icon-sort::before {
  content: "\ea15";
}

.icon-tiktok::before {
  content: "\ea16";
}

.icon-times::before {
  content: "\ea17";
}

.icon-truck::before {
  content: "\ea18";
}

.icon-twitter::before {
  content: "\ea19";
}

.icon-whatsapp::before {
  content: "\ea1a";
}

.icon-youtube::before {
  content: "\ea1b";
}


/* IE Hack */

@media all and (-ms-high-contrast: active), (-ms-high-contrast: none) {

    body{
        opacity: 0;
        visibility: hidden;
    }

    body.old-browser{
        opacity: 1;
        visibility: visible;
    }

    .old-browser-lost{
        height: 400px;
    }

}


/* Old Browser */

.old-browser{
    background-color: #fff;
    min-height: 100vh;
    height: 100%;
}

.old-browser .container{
    max-width: 700px;
}

.old-browser .old-browser-title{
    font-size: 1.875rem;
    font-weight: 600;
    text-align: center;
    margin: -1.875rem 0 1.875rem 0;
    color: #263238;
}

.old-browser .old-browser-lost{
    max-width: 400px;
    display: block;
    margin: 0 auto;
}

.old-browser .old-browser-info{
    font-size: 1rem;
    font-weight: 300;
    line-height: 1.6;
}

.old-browser .old-browser-info p{
    margin-bottom: 1.25rem;
    color: #263238;
}

.old-browser .old-browser-options::after {
    content: '';
    clear: both;
    display: table;
}

.old-browser .old-browser-options {
    margin: 30px auto;
    width: 520px;
}

.old-browser .old-browser-options li {
    float: left;
    width: 80px;
    text-align: center;
}

.old-browser .old-browser-options li + li{
    margin-left: 1.875rem;
}

.old-browser .old-browser-options li a{
    display: block;
    text-decoration: none;
    color: #263238;
    transition: ease-out 0.2s;
}

.old-browser .old-browser-options li a:hover{
    color: #2db5ff;
}

.old-browser .old-browser-options li img{
    width: 60px;
    height: 60px;
    margin: 0 auto;
}

.old-browser .old-browser-options li .label{
    font-size: 0.875rem;
    font-weight: 400;
    display: block;
    text-align: center;
    line-height: 1.4;
    margin-top: 0.625rem;
}

@media (max-width: 767px){

    .old-browser .container {
        padding: 0 20px;
    }

}

@media (max-width: 575px){

    .old-browser .old-browser-title{
        font-size: 1.25rem;
    }

    .old-browser .old-browser-options{
        width: 100%;
    }

    .old-browser .old-browser-options li{
        width: 50%;
        margin: 0 !important;
    }

    .old-browser .old-browser-options li:nth-child(n+3){
        margin: 30px 0 0 0 !important;
    }
}


/* Others */
.slcted{background-color:#ccc}.cl{clear:both;margin:0!important}.idp{font-size:10px}.is-hidden{display:none}.suggestion{z-index:100;border:6px solid #666;max-width:700px;min-width:400px;-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;left:auto}.suggestion span{display:block;cursor:pointer;padding:4px 0}.suggestion-words{font-size:13px;background-color:#fff;padding:14px}.suggestion-words span{border-bottom:1px dotted #ddd}.suggestion-products{background-color:#f5f5f5;padding:14px}.suggestion-products a{color:#000;font-weight:700}.suggestion-title{background-color:#d70008;color:#fff;display:block;margin-bottom:7px;text-indent:8px;font:700 14px/24px 'Open Sans Condensed',sans-serif}.suggestion-products .suggestion-img{width:90px;height:90px;float:left;margin-right:10px}.suggestion-product{margin-bottom:20px;font-size:13px}.suggestion-words span:hover{background:#eee}.suggestion-products strong,.suggestion-words strong{background:#666}.suggestion-product:hover{background:#d8d8d8}


/* Bootstrap Styles */

/* Flex */

.flex{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.flex-column{
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.justify-between{
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.justify-around{
    -ms-flex-pack: distribute;
    justify-content: space-around;
}

.justify-center{
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.justify-start{
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
}

.justify-end{
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}

.align-start{
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.align-center{
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.align-end{
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
}

.justify-self-end{
    -ms-grid-column-align: end;
    justify-self: end;
}

.align-content-center{
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.align-self-center{
    -ms-flex-item-align: center;
    -ms-grid-row-align: center;
    align-self: center;
}

.align-self-start{
    -ms-flex-item-align: start;
    align-self: flex-start;
}

.align-self-end{
    -ms-flex-item-align: end;
    align-self: flex-end;
}

.align-self-base{
    -ms-flex-item-align: baseline;
    align-self: baseline;
}

.f-wrap{
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.flex-grow{
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
}

.flex-1{
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.flex-2{
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

/* Containers */

.container{
    width: 100%;
    max-width: 1210px;
    margin: 0 auto;
    padding: 0 0.938rem;
}

/* Texts */

.text-line{
    white-space: nowrap;
}

.text-overflow{
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.decoration{
    text-decoration: underline;
}

.uppercase{
    text-transform: uppercase;
}

.user-select{
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Transitions */

.transition{
    transition: ease-in-out .3s;
}

.t-scale{
    -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63) !important;
    transition: -webkit-transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63) !important;
    -o-transition: transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63) !important;
    transition: transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63) !important;
    transition: transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63), -webkit-transform 0.3s cubic-bezier(0.53, 0.01, 0.36, 1.63) !important;
}

.t-color{
    transition: color ease-in-out .2s;
}

.t-bg{
    transition: background-color ease-in-out .2s;
}

.t-opacity{
    transition: opacity ease-in-out .2s;
}

/* Borders */

.rounded{
    border-radius: 50%;
    overflow: hidden;
}

.photo-rounded{
    position: relative;
    border-radius: 50%;
    overflow: hidden;
}

.photo-rounded::before{
    content: "";
    display: block;
    padding-bottom: 100%;
}

.photo-rounded img{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.photo-square{
    position: relative;
    border-radius: 50%;
    overflow: hidden;
}

.photo-square::before{
    content: "";
    display: block;
    padding-bottom: 100%;
}

.photo-square img{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.square{
    position: relative;
}

.square::after{
    content: "";
    display: block;
    padding-bottom: 100%;
}

/* Margins */

.off-margin-top{
    margin-top: 0 !important;
}

.off-margin-bottom{
    margin-bottom: 0 !important;
}

.off-margin-right{
    margin-right: 0 !important;
}

.off-margin-left{
    margin-bottom: 0 !important;
}


/* Accessibility */

.sr-only{
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}


/* Images */

img:not([src]){
    opacity: 0;
}

img.swiper-lazy{
    opacity: 0;
    transition: ease-in-out .2s;
}

img.swiper-lazy-loaded{
    opacity: 1;
}


/* Swiper */

.swiper-container-fade .swiper-slide.swiper-slide-active {
    opacity: 1!important;
}
.swiper-container-fade .swiper-slide {
    opacity: 0!important;
}


/* Modal tray */

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000;
}

.modal-backdrop.fade {
    filter: alpha(opacity=0);
    opacity: 0;
}

.modal-backdrop.in {
    filter: alpha(opacity=50);
    opacity: 0.5;
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -25%);
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal-open .modal {
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow: hidden;
}

.modal .close{
    font-size: 25px;
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 10;
    cursor: pointer;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: var(--color_font_medium);
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.15);
}

.modal .close span{
    display: flex;
    align-content: center;
    justify-content: center;
    margin: auto;
    height: 40px;
    font-family: var(--font_family);
}

.modal .modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}

.modal .modal-content {
    position: relative;
    outline: 0;
    color: var(--color_font_medium);
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid var(--color_gray_medium);
    border-radius: 6px;
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
}

.modal .modal-header {
    min-height: 16.43px;
    padding: 15px;
    border-bottom: 1px solid var(--color_gray_medium);
}

.modal .modal-title {
    margin: 0;
    line-height: 1.43;
    padding: 0 2.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    color: var(--color_font_dark);
}

.modal .modal-body{
    position: relative;
    padding: 15px;
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-color: var(--color_primary_medium) var(--color_gray_medium);
    scrollbar-width: thin
}
.modal .modal-body::-webkit-scrollbar{
    width: 6px;
    background: var(--color_gray_medium);
}
.modal .modal-body::-webkit-scrollbar-track{
    background: var(--color_gray_medium);
}
.modal .modal-body::-webkit-scrollbar-thumb{
    background: var(--color_primary_medium);
    border-radius: 5px;
}

.cart-preview-table{
    width: 100%
}

.cart-preview-item td {
    padding: 0;
    padding-top: 12px!important;
    padding-bottom: 12px!important;
    border: none;
}

.modal .cart-preview-item-image-box{
    width: 60px;
    padding-left: 10px!important;
    padding-right: 10px!important;
    vertical-align: middle;
}

.cart-preview-item-image-quantity-box{
    width: 60px;
    min-height: 60px;
}

.cart-preview-quantity-tag-box {
    background: #d3d1d1;
    color: #fff;
    font-size: 12px;
    border-radius: 16px;
    font-weight: 700;
    text-align: center;
    display: block;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 5px;
    position: absolute;
    margin-left: 46px;
    margin-top: -13px;
    box-sizing: content-box;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
}

.cart-preview-item-image-space-box {
    width: 50px;
    height: 50px;
    padding: 4px;
    border: 1px solid #d3d1d1;
    box-sizing: content-box;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
}

.cart-preview-item-image {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    max-width: 50px!important;
    max-height: 50px!important;
}

.cart-preview-item-name-box {
    padding-left: 10px!important;
    text-align: left;
    vertical-align: middle;
}

.cart-preview-item-name-box .cart-preview-item-name {
    font-weight: 500;
    color: var(--color_font_dark);
    display: block;
    font-size: .87rem;
}

.cart-preview-item-price-box {
    width: 115px;
    font-size: .87rem;
    text-align: right;
    vertical-align: middle;
}

.cart-preview-item-price-box small{
    font-size: .61rem;
    display: block;
}

.cart-preview-item-delete-box {
    width: 60px;
    padding-right: 10px!important;
    text-align: right;
    vertical-align: middle;
}

.cart-preview-item-delete {
    font-size: .75rem;
    cursor: pointer;
    text-decoration: underline;
    display: block;
    margin-left: 5px;
}

.modal .modal-footer {
    padding: 15px 15px 30px;
    text-align: right;
    border-top: 1px solid var(--color_gray_medium);
}

.modal .modal-footer .cart-preview-subtotal{
    margin-bottom: 15px;
    font-size: .87rem;
}

.modal.cart-preview .modal-content .currency{
    text-decoration: none;
}

.modal .modal-footer .botao-continuar-comprando{
    float: left;
    cursor: pointer;
}

.modal-footer .btn + .btn {
    margin-bottom: 0;
    margin-left: 5px;
}

.modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
    margin-left: 0;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}

.modal-dialog-center {
    margin: 0;
    position: absolute !important;
    top: 50%;
    left: 50%;
}

.modal-body #Page {
    width: auto !important;
    display: block !important;
}

@media (min-width: 992px){
    .modal-lg {
        width: 900px;
    }
}

@media (min-width: 768px) {

    .modal .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }

    .modal .modal-content {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }

    .modal .modal-sm {
        width: 300px;
    }

}

@media (max-width: 767px){

    .modal .modal-title{
        font-size: 0.75rem;
        padding: 0 0.875rem;
    }

    .modal-dialog-center {
        width: 100%;
    }

}

@media (max-width: 460px){

    .modal .cart-preview-item-image-box{
        padding-left: 0!important;
        padding-right: 0!important;
    }

    .cart-preview-item-name-box {
        padding-left: 4px !important;
    }

    .cart-preview-item-name-box .cart-preview-item-name,
    .cart-preview-item-price-box {
        font-size: .75rem;
    }

    .modal .modal-footer .botao-continuar-comprando,
    .modal .modal-footer .botao-prosseguir-compra{
        float: none;
        display: block;
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .modal .modal-footer .botao-prosseguir-compra{
        margin-bottom: 0;
    }

}


/* Resets */

*{
    margin: 0;
    padding: 0;
    outline: 0;
    box-sizing: border-box;
}

body{
    min-width: 360px;
    font-family: var(--font_family);
    font-size: 16px;
    line-height: 1.4;
    color: var(--color_font_medium);
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

[hidden]{
    display: none !important;
}

ol, ul {
    list-style: none;
}

th,td{
    vertical-align: middle;
}

a{
    text-decoration: none;
    color: inherit;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

.clear{
    clear: both;
}

.clear-content::after{
    content: "";
    display: block;
}

input:not([type="radio"]):not([type="checkbox"]),
select,
textarea{
    font-family: inherit;
    -webkit-appearance: none;
    font-weight: normal;
    background: var(--color_gray_medium);
}

input::-ms-clear {
    display:none;
}

input:not([type="radio"]):not([type="checkbox"])::placeholder,
textarea::placeholder{
    color: var(--color_font_medium);
    opacity: 1;
}

input:not([type="radio"]):not([type="checkbox"]):focus,
textarea:focus{
    box-shadow: inset 0 0 8px var(--color_gray_dark);
}

select::-ms-expand{
    display: none;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance:textfield !important;
}


/* Image Manipulation */

img.transform {
    position:absolute;
    top:50%;
    left:50%;
    transform:translate3d(-50%,-50%,0);
    opacity:0;
    transition: ease-out 0.2s
}

img.transform.loaded,
img.transform.swiper-lazy-loaded {
    opacity:1
}

img.transform.vertical {
    width:100%
}

img.transform.horizontal {
    height:100%
}


/* Tray Components */

.suggestion{
    overflow: auto;
    max-height: calc(100vh - 120px);
}

.tray-hide,
.compare-hidden{
    display: none !important
}

.carrinho-tabs{
    display: none;
}

.modal{
    display: none;
}

.sec_texto{
    color:#999
}
.sec_baixa{
    color:red
}
.sec_media{
    color:#F60
}
.sec_alta{
    color:green
}
.safe{
    margin-bottom:-5px
}

.hidden{
    display: none;
}

#aviso_depoimento{
    margin: 10px 0;
    text-align: center;
}

.banner-bottom{
    margin-top: 30px;
}

.banner-bottom img{
    display: block;
    max-width: 100%;
    margin: auto;
}

.page-content .container .container{
    padding: 0;
}

#ProdBlock.prodBox {
    display: none;
}

.container,
.container2{
    background: transparent;
    position: static;
}

.container2{
    float: none;
    background: transparent;
    position: static;
}

.container3{
    float: none;
    background: transparent;
    position: static;
    border: 0;
}

.line{
    border: 0;
    height: auto;
    width: auto;
}

.bottom,.topBorder{
    background: transparent;
}

.modal-header{
    min-height: 46px;
}

.leftCorner{
    display: none;
}

.bottom .bLeft,
.bottom .bRight{
    display: none;
}

.obriga-barra.red{
    font-size: 0;
}

.obriga-barra.red::before{
    content: "*";
    font-size: 16px;
}

.botao-calcular-frete,
.botao-calcular-frete:hover,
.botao-cupom-desconto,
.botao-cupom-desconto:hover,
.botao-efetuar-login,
.botao-efetuar-login:hover,
.botao-enviar-cadastro,
.botao-enviar-cadastro:hover,
.botao-enviar-cartao,
.botao-enviar-cartao:hover,
.botao-finalizar-compra,
.botao-finalizar-compra:hover,
.botao-novo-cadastro,
.botao-novo-cadastro:hover,
.botao-prosseguir-cadastro,
.botao-prosseguir-cadastro:hover,
.botao-prosseguir-compra,
.botao-prosseguir-compra:hover,
.botao-salvar-lista,
.botao-salvar-lista:hover,
.botao-simular-frete,
.botao-simular-frete:hover,
a.botao-calcular-frete,
a.botao-cupom-desconto,
a.botao-efetuar-login,
a.botao-enviar-cadastro,
a.botao-enviar-cartao,
a.botao-finalizar-compra,
a.botao-novo-cadastro,
a.botao-prosseguir-cadastro,
a.botao-prosseguir-compra,
a.botao-salvar-lista,
a.botao-simular-frete{
    background: unset;
    border: unset;
    color: unset;
}

#loading-product-container{
    z-index: 6;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#loading-product-container::before{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: .6;
}

.botao-commerce:not(.botao-sob-consulta):not(.botao-nao_indisponivel){
    padding: 0.625rem 1.25rem;
    font-family: var(--font_family);
    font-size: 0.75rem;
    font-weight: 700;
    color: var(--color_font_inverted) !important;
    background-color: var(--color_secondary_medium);
    border-radius: 4px;
    transition: ease-out 0.2s;
    text-transform: uppercase;
}

.botao-commerce:hover{
    opacity: .8;
}

.modal.cart-preview .botao-commerce.botao-continuar-comprando {
    padding: 0;
}

.botao-commerce.botao-continuar-comprando .botao-commerce-img {
    padding: 0.625rem 1.25rem;
    display: block;
}

#wrapper{
    transform: inherit !important;
    padding: 0 !important;
}

.brinde_lista{
    display: flex;
    flex-wrap: wrap;
    width: auto !important;
    margin: 0 -0.3125rem;
    justify-content: center;
}

.brinde_lista img{
    border: 2px solid var(--color_gray_dark)!important;
}

.brinde_lista img[style*="border: 2px"]{
    box-shadow: 0 0 0 2px var(--color_primary_medium);
}

.brinde_lista span{
    display: block;
    cursor: pointer;
}

.brinde_lista li{
    margin-top: 5px;
    padding: 0 5px;
}

.brinde_detalhes .botao{
    position: relative;
    display: inline-block;
    cursor: pointer;
    z-index: 1;
}

.brinde_detalhes .botao::before{
    content: 'ESCOLHER';
    display: block;
    width: 100px;
    color:#fff;
    font-size: .75rem;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    line-height: 40px;
    text-align: center;
    font-size: .75rem;
    background-color: var(--color_secondary_medium);
}

.brinde_detalhes .botao img{
    opacity: 0;
    display: block;
    width: 100px;
    height: 40px;
}

.varTit{
    font-size: .875rem;
    font-weight: 500;
}

.varTit *{
    font-size: inherit;
    font-weight: inherit;
}

.load-css .icon{
    left: 50%;
    top: 50%;
    position: absolute;
    -webkit-transform: translate3d(-50%,-50%,0);
            transform: translate3d(-50%,-50%,0);
    width: 30px;
    height: 30px;
}

.load-css .icon::before{
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: 3px solid #9c9c9c;
    border-radius: 50%;
}

.load-css .icon::after{
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border: 3px solid #000;
    border-right-color: transparent;
    animation: rotate infinite linear .6s;
    border-radius: 50%;

}

@keyframes rotate{
    0%   { transform: rotate(0deg);   }
    100% { transform: rotate(360deg); }
}


#letMeKnow img{
    display: none;
}

#letMeKnow::before{
    content: "Avise-me";
    display: inline-block;
    background: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    line-height: 52px;
    padding: 0 30px;
    font-weight: 400;
    height: 52px;
    vertical-align: bottom;
    border-radius: 0 3px 3px 0;
    width: 160px;
    text-align: center;
    transition: ease-out 0.2s;
}

#letMeKnow:hover::before{
    opacity: .8;
}

.botao-nao_indisponivel{
    background: transparent !important;
    padding: 0 !important;
    font-size: 0 !important;
    text-align: center;
}

#nao_disp{
    font-size: .875rem;
    border-radius: 8px;
}

#nao_disp h3{
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    width: 100%;
}

#nao_disp h5{
    font-size: .875rem;
    font-weight: 400;
    margin: 5px 0 0;
}

#nao_disp .color{
    font-size: .75rem;
    margin-top: 10px;
    display: none;
}

#letmeknow_response label,
#nao_disp label{
    display: inline-block;
    width: calc(100% - 186px);
    margin-top: 23px;
    max-width: 228px;
}

@media (max-width: 550px) {
    #letmeknow_response label,
    #nao_disp label{
        width: 100%;
        max-width: unset;
    }

    #letMeKnow::before{
        width: 100%;
        text-align: center;
    }
}

#email_avise{
    display: block;
    width: 100%;
    border-radius: 0;
}

.Mapa{
    display: none;
}

input, select, textarea{
    border: 0;
}

button{
    border: 0;
    cursor: pointer;
}

p,ul,li{
    border: 0;
}

input.text,
select.select,
select.text,
.campoform:not([type="checkbox"]){
    padding: 0 1.25rem;
    height: 52px;
    font-size: .875rem;
    line-height: 1.4;
    color: var(--color_font_medium);
    border: 1px solid var(--color_gray_dark);
    border-radius: 3px;
}

input.text.obriga,
select.obriga{
    padding-left: 17px;
}

input.text:disabled,
select.select:disabled,
select.text:disabled{
    background-color: var(--color_gray_medium) !important;
}

select.text,
select.select{
    -webkit-appearance: none;
    background-repeat: no-repeat;
    background-image: var(--arrow_select) !important;
    background-size: 9.2px;
    background-position: calc(100% - 10px) center !important;
    padding-right: 22px !important;
    cursor: pointer;
    max-width: 100%;
}

select.text::-ms-expand,
select.select::-ms-expand{
    display: none;
}

textarea.textarea,
textarea#mensagem{
    border: 1px solid var(--color_gray_dark);
    padding: 0.875rem;
    font-size: 0.875rem;
    border-radius: 2px;
    color: var(--color_font_medium);
}

.blocoSucesso{
    color: #00ad29;
    margin: 10px 0;
    background: #f1f9f3;
    padding: 0.5rem;
    font-size: 0.75rem;
    text-align: center;
    border-radius: 4px;
}

form[action*="question"] .text,
form[action*="question"] .textarea{
    resize: none;
    display: block;
    max-width: 384px;
    margin: 9px auto 0;
}

form[action*="question"] fieldset{
    font-size: 0;
}

form[action*="question"] fieldset .obriga.red{
    display: none;
}

form[action*="question"] fieldset p{
    font-size: 0;
}

#div_atualiza{
    text-align: center;
}

#div_atualiza > p:first-of-type{
    font-size: 0;
}

#div_atualiza > p:first-of-type::before{
    content: 'Preencha os campos abaixo para conhecermos um pouco melhor as suas necessidades!';
    display: block;
    font-size: 1rem;
}

form[action*="question"] fieldset p label{
    font-size: 0.875rem;
    font-weight: 700;
    text-align: center;
    display: block;
    margin-top: 23px;
}

form[action*="question"] fieldset p label:nth-of-type(2),
form[action*="question"] fieldset p label:nth-of-type(4),
form[action*="question"] fieldset p label:nth-of-type(5),
form[action*="question"] fieldset p label:nth-of-type(6){
    display: none;
}

.varCont textarea.textarea{
    width: 100%;
}

.modal-dialog-center{
    max-width: 92%;
}

#form1 input,
#form1 select,
#form1 textarea{
    width: 100%;
    display: block;
}

.MapaSite{
    width: 100%;
    margin: 15px 0;
    overflow: auto;
}

p{
    padding: 0;
    position: static;
}

.MapaSite > ul{
    min-width: 600px;
}

.MapaSite h2{
    background: transparent;
}

@media (max-width: 767px){
    .container{
        padding: 0 0.625rem;
    }

    .row{
        margin: 0 -0.625rem;
    }

    .row .col{
        padding: 0 0.625rem;
    }
}

input[size]{
    max-width: 100%;
}

#modal-form-content h3{
    font-size: 1.125rem;
}

#modal-form-content fieldset{
    border: 0;
}

.compare-buttons{
    text-align: center;
    font-size: .75rem;
    margin: 6px 0;
}

.compare-buttons a:hover{
    color: var(--color_primary_medium);
}

.compare-buttons .filter-checkbox{
    display: flex;
    justify-content: center;
    width: 14px;
    height: 14px;
    border: 1px solid var(--color_gray_dark);
    align-items: center;
    margin-right: 5px;
}

.compare-buttons [data-compare="remove"] .filter-checkbox::before{
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    background-color: var(--color_primary_medium);
}


@media (min-width: 931px){

    .catalog-header .catalog-info .system-filter{
        order: 3;
    }

    .compare-wrapper{
        font-size: .87rem;
        text-transform: uppercase;
        font-weight: 700;
        height: 33px;
        line-height: 33px;
        padding: 0 23px;
        white-space: nowrap;
        background: var(--color_secondary_medium);
        margin-right: 10px;
        color: #fff;
        border-radius: 3px;
        order: 2;
        margin-left: auto;
        transition: ease-out 0.2s;
    }

    .compare-wrapper:hover{
        opacity: .8;
    }

}

@media (max-width: 930px){

    .compare-wrapper{
        order: 3;
        width: 100%;
        margin-top: 1.25rem;
    }

    .button-compare{
        line-height: 40px;
        height: 40px;
        display: block;
        width: 200px;
        margin: 0 auto;
        text-align: center;
        font-size: .75rem;
        text-transform: uppercase;
        font-weight: 700;
        padding: 0 23px;
        white-space: nowrap;
        background: var(--color_secondary_medium);
        color: var(--color_font_inverted);
        border-radius: 3px;
    }

}

@media (max-width: 575px){
    .button-compare{
        width: 100%;
    }
}

.comparatorTabs{
    margin: 15px 0;
}

.comparatorTabs ul{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.comparatorTabs ul li{
    position: relative;
    margin-right: 5px;
}

.comparatorTabs ul li a:first-child{
    padding: 5px 10px;
    padding-right: 30px;
    display: block;
}

.comparatorTabs ul li a:last-child{
    position: absolute;
    right: 5px;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    border: 0;
    line-height: 22px;
    font-weight: 700;
    text-align: center;
    font-size: 13px;
}

.comparatorTabs ul li.aberta a:first-child{
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    border-radius: 2px;
}

.comparator{
    overflow: auto;
    width: 100%;
    margin-bottom: 20px;
}

.comparator ul{
    width: 100%;
}

.comparator ul ul{
    display: table;
    table-layout: fixed;
    border-collapse: collapse;
}

.comparator ul ul > li{
    display: table-cell;
    width: 150px;
    padding: 10px;
    border: 1px solid var(--color_gray_dark);
    border-top: 0;
    border-collapse: collapse;
}

.comparator ul .Labels ul > li{
    border-top: 1px solid var(--color_gray_dark);
    font-weight: 600;
}

.comparator ul ul > li.comparsionFoto{
    width: 250px;
}
.comparator ul ul > li.comparsionInfoPreco{
    width: 250px;
}
.comparator ul ul > li.comparsionRate {
    width: 180px;
}

.comparator ul ul > li.comparsionDescricao  {
    width: 190px;
}
.comparator ul ul > li.displayMarca  {
    width: 120px;
}
.comparator ul ul > li.displayModelo  {
    width: 140px;
}
.comparator ul ul > li.displayGarantia  {
    width: 140px;
}
.comparator ul ul > li.displayDisponibilidade  {
    width: 180px;
}

.comparator .comparsionFoto{
    position: relative;
}

.comparator .comparsionFoto .closeComp{
    position: absolute;
    right: 5px;
    top: 0;
    width: 16px;
    height: 16px;
    font-size: .75rem;
    display: block;
    text-align: center;
    line-height: 14px;
    border: 1px solid #565656;
    background-color: #fff;
}

.comparator .comparsionFoto a{
    font-size: .875rem;
    line-height: 1.2;
    display: block;
    margin: 0 0 10px;
    max-height: 142px;
    overflow: hidden;
}

.comparator .comparsionFoto a[title="Escolher"] img,
.comparator .comparsionFoto a[alt="Comprar"] img{
    display: none;
}

.comparator .comparsionFoto a img {
    max-height: 140px;
    max-width: 100%;
}

.comparator .comparsionFoto a[title="Escolher"]::before,
.comparator .comparsionFoto a[alt="Comprar"]::before{
    content: 'COMPRAR';
    display: inline-block;
    line-height: 34px;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    cursor: pointer;
    padding: 0 10px;
    text-transform: uppercase;
    font-size: .75rem;
    font-weight: 600;
    border-radius: 2px;
}

.comparator .comparsionFoto a[title="Escolher"]::before{
    content: 'ESCOLHER';
}

.comparator .comparsionFoto .closeComp{
    border-radius: 50%;
    width: 22px;
    height: 22px;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    border: 0;
    line-height: 22px;
    font-weight: 700;
    margin-top: 10px;
}

.comparsionInfoPreco br + br{
    display: none;
}

.page-comparador h1{
    color: var(--color_font_dark);
    font-weight: 600;
    font-size: 28px;
    margin-bottom: 30px;
    text-align: center;
}

.page-comparador h1::after {
    content: '';
    display: block;
    width: 62px;
    height: 4px;
    margin: 15px auto 0;
    background-color: var(--color_primary_medium);
}

.page-comparador .page-content > .container > .board > .container3 > .container2 > .board .left{
    font-size: 14px;
    font-weight: 400;
}

.page-comparador .page-content > .container > .board > .container3 > .container2 > .board .left strong{
    font-weight: 400;
}

.page-comparador .page-content > .container > .board > .container3 > .container2 > .board{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page-comparador .page-content > .container > .board > .container3 > .container2 > .board .right{
    display: flex;
}

.page-comparador .page-content > .container > .board > .container3 > .container2 > .board .right > *{
    min-width: 44px;
    height: 38px;
}

@media (max-width: 767px){

    .page-comparador .page-content > .container > .board > .container3 > .container2 > .board {
        flex-direction: column-reverse;
    }

    .page-comparador .page-content > .container > .board > .container3 > .container2 > .board .left{
        margin-top: 15px;
    }

}

.box-shadow{
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
}

.box-alerts{
    padding: 5px;
    font-size: .75rem;
    color: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    left: 0;
    z-index: 9;
    right: 0;
    background: #a51111;
    text-align: center;
}

#alert-show{
    display: none;
}

#alert-show:checked ~ .box-alerts{
    display: none;
}

#alert-show + .infobox.danger, #alert-show + .box-alerts{
    display: none;
}

.box-alerts:empty{
    display: none;
}

.close-info:first-child{
    display: none;
}

.close-info{
    position: absolute;
    top: 0;
    width: 14px;
    height: 14px;
    cursor: pointer;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    right: 10px;
    bottom: 0;
    margin: auto;
    display: block;
}

.close-info::before{
    content: '';
    display: block;
    width: 14px;
    height: 2px;
    background: #1e201d;
    position: absolute;
    top: 6px;
}

.close-info::after{
    content: '';
    display: block;
    height: 14px;
    width: 2px;
    background: #1e201d;
    position: absolute;
    left: 6px;
}

@media (max-width: 395px) {
    .infobox.danger + .close-info {
        top: 32px;
    }
}

@media (max-width: 431px) and (min-width: 395px) {
    .infobox.danger + .close-info {
        top: -16px;
    }
}

@media (max-width: 802px) and  (min-width: 431px) {
    .infobox.danger + .close-info {
        top: 18px;
    }
}

.infobox.danger + .close-info::before,
.infobox.danger + .close-info::after {
    background: #fff;;
}


.box-alerts .infobox.danger{
    font-size: .75rem;
    padding: unset;
    color: #fff;
}

.box-alerts .infobox{
    padding: 10px;
    font-size: .75rem;
    padding-right: 30px;
    color: #707070;
}

.title-store{
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
}

.perguntasProdutoBTimg{
    float: none !important;
    margin: 1.25rem auto 0;
    display: block;
    width: 160px !important;
    height: 42px !important;
    font-family: var(--font_family);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 2px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    text-transform: uppercase;
    transition: ease-out 0.2s;
}

.perguntasProdutoBTimg:hover{
    opacity: .8;
}


/* Rich text editor */

.rte,
.board_htm{
    width: 100%;
    overflow: hidden;
}

.page-noticia .board::after,
.board_htm::after{
    content: '';
    display: block;
    clear: both;
}

.page-noticia .board h1,
.rte h1,
.board_htm h1{
    font-size: 1.675rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-noticia .board h2,
.rte h2,
.board_htm h2{
    font-size: 1.425rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-noticia .board h3,
.rte h3,
.board_htm h3{
    font-size: 1.35rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-noticia .board h4,
.board_htm h4,
.rte h4{
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-noticia .board h5,
.board_htm h5,
.rte h5{
    font-size: 1.1125rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-noticia .board h6,
.board_htm h6,
.rte h6{
    font-size: 1rem;
    margin-bottom: 16px;
    font-weight: 700;
}

.board_htm img,
.rte img{
    max-width: 100%;
    height: auto !important;
    margin: 10px 0;
}

.board_htm img[style*="float: right"],
.rte img[style*="float: right"]{
    margin: 10px 0 10px 10px;
}

.board_htm img[style*="float: left"],
.rte img[style*="float: left"]{
    margin: 10px 10px 10px 0;
}

.page-noticia .board iframe,
.board_htm iframe,
.rte iframe{
    max-width: 100%;
}

.rte,
.board_htm{
    font-family: inherit;
    font-size: 1rem;
    line-height: 25px;
}

.board_htm p,
.rte p{
    margin-bottom: 14px;
}

.board_htm p:last-child,
.rte p:last-child {
    margin-bottom: 0;
}

.page-noticia .board a,
.board_htm a,
.rte a{
    text-decoration: underline;
}

.page-noticia .board a:hover,
.board_htm a,
.rte a:hover{
    color: var(--color_primary_medium);
}

.page-noticia .board ul,
.board_htm ul,
.rte ul{
    list-style: disc;
    margin-left: 1.875rem;
    margin-bottom: 1.25rem;
    line-height: 1.7;
}

.page-noticia .board ol,
.board_htm ol,
.rte ol{
    list-style: decimal;
    margin-left: 1.875rem;
    margin-bottom: 1.25rem;
    line-height: 1.7;
}

.board_htm table,
.rte table{
    width: 100%;
}

.board_htm th,
.board_htm td,
.rte table th,
.rte table td{
    padding: 0.3125rem 0.625rem;
}

.board_htm table tr:nth-child(odd) td,
.rte table tr:nth-child(odd) td{
    background-color: rgba(0, 0, 0, 0.05);
}

.rte table tr:nth-child(even) td,
.board_htm table tr:nth-child(even) td{
    background-color: rgba(0, 0, 0, 0.02);
}

.rte .rte-video-wrapper,
.board_htm .rte-video-wrapper,
.page-noticia .board .rte-video-wrapper{
    position: relative;
    overflow: hidden;
    max-width: 100%;
    height: auto;
    padding-bottom: 56.25%;
}

.rte .rte-video-wrapper .iframe,
.board_htm .rte-video-wrapper .iframe,
.rte .rte-video-wrapper iframe,
.board_htm .rte-video-wrapper iframe,
.page-noticia .board .rte-video-wrapper iframe{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Main Theme Styles */

body.overflowed{
    overflow: hidden;
}


/* Video */

.video{
    position: relative;
}

.video::before{
    content: '';
    display: block;
    padding-bottom: 56.25%;
}

.video iframe{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    border: 0;
}

/* modal theme */

.modal-theme{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 30;
    overflow: auto;
    opacity: 0;
    visibility: hidden;
    transition: ease-in-out .3s;
}

.modal-theme.show{
    opacity: 1;
    visibility: visible;
}

.modal-theme .modal-shadow{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-theme .close-icon{
    position: absolute;
    top: -20px;
    right: -20px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    cursor: pointer;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: var(--color_font_medium);
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.15);
}

.modal-theme .close-icon .icon{
    height: 20px;
    display: block;
}

.modal-theme .modal-scroll{
    width: 100%;
    height: 100%;
    overflow: auto;
}

.modal-theme .modal-wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 100%;
    padding: 2.5rem;
}

.modal-theme .modal-info{
    position: relative;
    width: 100%;
    min-width: 200px;
    max-width: 1000px;
    padding: 2.5rem;
    color: var(--color_font_medium);
    background: #fff;
    border-radius: 6px;
    transition: transform cubic-bezier(.54, -.65, .48, 1.64) 0.3s;
    transform: scale(.6);
    z-index: 2;
}

.modal-theme.show:not(.loaded) .modal-info{
    transform: scale(1);
}

.modal-theme .modal-content img{
    display: block;
    max-width: 100%;
}

.modal-theme.modal-video .modal-info{
    padding: 0;
}

.modal-theme.modal-video .video{
    overflow: hidden;
    border-radius: 6px;
}

.modal-theme.modal-store-reviews .modal-info{
    max-width: 500px;
}

.modal-theme .append{
    padding: 30px 20px 20px;
}

.modal-theme .light_altura{
    width: 400px !important;
    max-width: 100%;
}

.modal-theme .light_altura h2{
    font-weight: 600;
    text-transform: uppercase;
    font-size: .875rem;
}

.modal-theme .light_altura hr{
    display: none;
}

@media (min-width: 991px){
    .modal-theme .append{
        padding: 30px 40px;
    }
}

@media (max-width: 767px){

    .modal-theme .modal-wrapper{
        height: auto;
    }

}

@media (max-width: 575px){

    .modal-theme .modal-wrapper{
        padding: 1.25rem;
    }

    .modal-theme .modal-info{
        padding: 1.25rem;
    }
}




/* Application */

@media (min-width: 768px){

    .application{
        padding-top: 192px;
    }

}


/* Error message */

.error-message{
    padding: 0.50rem;
    font-size: 0.75rem;
    text-align: center;
    border-radius: 4px;
    color: #e15656;
    background-color: #ffebeb;
}


/* Swiper slider arrows */

.swiper-carousel{
    position: relative;
}

.swiper-carousel .prev,
.swiper-carousel .next,
.swiper-container .prev,
.swiper-container .next {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 28px;
    height: 48px;
    cursor: pointer;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    z-index: 3;
    border-radius: 2px;
    line-height: 52px;
    text-align: center;
    margin: auto;
}

.swiper-carousel .prev,
.swiper-container .prev{
    left: 0;
}

.swiper-carousel .next,
.swiper-container .next{
    right: 0;
}

.swiper-carousel .prev.swiper-button-disabled,
.swiper-carousel .next.swiper-button-disabled,
.swiper-container .prev.swiper-button-disabled,
.swiper-container .next.swiper-button-disabled{
    opacity: 0;
    visibility: hidden;
}

@media (max-width: 767px){

    .swiper-carousel .prev,
    .swiper-carousel .next,
    .swiper-container .prev,
    .swiper-container .next{
        display: none;
    }

}


/* Swiper slider dots */

.swiper-container .dots{
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 1.875rem;
}

.swiper-container .dots .dot{
    width: 8px;
    margin: 0 5px;
    padding: 0;
    cursor: pointer;
    padding: 5px 0;
}

.swiper-container .dots .dot::after{
    content: '';
    display: block;
    height: 8px;
    border-radius: 4px;
    background-color: var(--color_font_light);
    transition: ease-out 0.2s;
    opacity: .4;
}

.swiper-container .dots .dot-active::after{
    opacity: 1;
}

@media (min-width: 768px){
    .swiper-container.hide-dots-desk .dots{
        display: none;
    }
}


/* Sections header */

.section-header{
    text-align: center;
    margin-bottom: 1.875rem;
}

.section-header .title-section{
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    color: var(--color_font_dark);
}

.section-header .subtitle-section{
    font-size: 1.125rem;
    font-weight: 500;
    text-align: center;
    color: var(--color_font_light);
}

@media (max-width: 575px){

    .section-header .title-section{
        font-size: 1.25rem;
    }

    .section-header .subtitle-section{
        font-size: 0.875rem;
    }

}


/* Pages */

@media (min-width: 992px){

    .page-content:not(.not-padding),
    .page-search .page-content,
    .page-catalog .page-content {
        padding-top: 40px;
    }

}

@media (max-width: 991px){

    .page-search .page-content, 
    .page-catalog .page-content,
    .page-content:not(.not-padding){
        padding-top: 20px;
    }

}

/* Autocomplete Search */

.suggestion{
    position: absolute;
    top: 100%;
    left: 0;
    max-width: unset;
    max-height: 450px;
    min-width: unset;
    width: 100%;
    padding: 0 1.25rem;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.16);
    background-color: #fff;
    border: none;
    border-radius: 0 0 3px 3px;
    scrollbar-color: var(--color_header_highlight) #fff;
    scrollbar-width: thin
}
.suggestion::-webkit-scrollbar{
    width: 6px;
    background: #fff;
}
.suggestion::-webkit-scrollbar-track{
    background: #fff;
}
.suggestion::-webkit-scrollbar-thumb{
    background: var(--color_header_highlight);
    border-radius: 5px;
}

.suggestion:hover{
    display: block !important;
}

.suggestion .idp,
.suggestion .cl{
    display: none;
}

.suggestion-title{
    font-family: inherit;
    line-height: 1.4;
    font-size: 0.875rem;
    font-weight: 600;
    text-indent: 0;
    margin-bottom: 10px;
    text-transform: uppercase;
    background-color: transparent !important;
    color: var(--color_font_dark);
}

.suggestion-words{
    margin-bottom: 1.875rem;
    padding: 1.25rem 0 0 0;
    background: transparent;
}

.suggestion-words .suggestion-title{
    margin-bottom: 0.3125rem;
}

.suggestion-words span{
    font-size: 0.875rem;
    border: 0;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    background-color: transparent;
    transition: ease-out 0.2s;
}

.suggestion-words span:hover{
    color: var(--color_primary_medium);
    background-color: transparent;
}

.suggestion-products{
    width: 100%;
    padding: 0;
    background: transparent;
}

.suggestion-products .suggestion-product:hover {
    background: transparent;
}

.suggestion-products .suggestion-product a{
    display: flex;
    margin: 0;
    padding: 0;
}

.suggestion-products .suggestion-product a:hover{
    background: transparent;
}

.suggestion-products .suggestion-product .suggestion-img{
    width: 50px;
    height: 50px;
    float: unset;
    margin-right: 0.625rem;
}

.suggestion-products .suggestion-product .suggestion-img img{
    width: 100%;
    height: 100%;
}

.suggestion-products .suggestion-product .suggestion-desc{
    width: calc(100% - 60px);
}

.suggestion-products .suggestion-product .suggestion-desc .titlep{
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color_font_light);
    transition: ease-out 0.2s;
}

.suggestion-products .suggestion-product .suggestion-desc .titlep:hover{
    color: var(--color_primary_medium);
}


/* Loaders */

.loader:not(img){
    position: absolute;
    z-index: 5;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    background-color: rgba(255,255,255, 0.8);
    transition: ease-out 0.2s;
}

.loader.show{
    opacity: 1;
    visibility: visible;
}

.loader .spinner {
    width: 40px;
    height: 40px;
    position: relative;
}

.loader .spinner .double-bounce-one,
.loader .spinner .double-bounce-two {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--color_primary_medium);
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: sk-bounce 2.0s infinite ease-in-out;
}

.loader .spinner .double-bounce-two {
    animation-delay: -1.0s;
}

.loader .message{
    font-size: 0.875rem;
    margin-top: 0.625rem;
    color: var(--color_font_dark);
}

@keyframes sk-bounce {
    0%, 100% { transform: scale(0.0); }
    50% { transform: scale(1.0); }
}

/* Floating WhatsApp */

.floating-whatsapp{
    position: fixed;
    display: flex;
    bottom: 70px;
    font-size: 1rem;
    z-index: 9;
}

.floating-whatsapp.on-left{
    left: 50px;
}

.floating-whatsapp.on-right{
    right: 50px;
}

.floating-whatsapp a{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: ease-out 0.2s;
    color: #fff;
}

.floating-whatsapp a{
    background-color: #25d366;
}

.floating-whatsapp a:hover{
    background-color: #23c35f;
}

.floating-whatsapp a .icon{
    font-size: 2.0625rem;
}

@media (max-width: 767px){

    .floating-whatsapp.on-left{
        left: 10px;
    }

    .floating-whatsapp.on-right{
        right: 10px;
    }

}


/* Shadows */

.overlay-shadow {
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 19;
    opacity: 0;
    visibility: hidden;
    background-color: #000;
    transition: ease-out 0.3s;
}

.overlay-shadow.show{
    opacity: 0.5;
    visibility: visible;
}


/* Close button modal */

.close-box{
    position: absolute;
    top: 2.8125rem;
    right: 3.625rem;
    font-size: 1.125rem;
    width: 22px;
    height: 22px;
    cursor: pointer;
}

.close-box:hover .icon{
    transition: ease-in-out .5s;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

/* Header search */

.header-search-wrapper{
    position: relative;
    width: 100%;
}

header .header-search-wrapper .input-search{
    display: block;
    width: 100%;
    height: 52px;
    padding: 0 58px 0 20px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 6px;
    color: var(--color_header_second_font);
    background: var(--color_header_details);
    text-transform: uppercase;
}

header .header-search-wrapper .input-search:focus{
    box-shadow: inset 0 0 4px var(--color_header_second_font);
}

header .header-search-wrapper .input-search::-webkit-input-placeholder{
    color: var(--color_header_second_font)!important;
}
header .header-search-wrapper .input-search:-ms-input-placeholder{
    color: var(--color_header_second_font) !important;
}
header .header-search-wrapper .input-search::-ms-input-placeholder{
    color: var(--color_header_second_font) !important;
}
header .header-search-wrapper .input-search::placeholder{
    color: var(--color_header_second_font) !important;
}

.header-search-wrapper .button-search{
    position: absolute;
    width: 58px;
    height: 100%;
    top: 0;
    right: 0;
    font-size: 20px;
    color: var(--color_header_highlight);
    background-color: transparent;
}

@media (max-width: 500px){
    .header .header-search-wrapper .input-search{
        font-size: 16px;
    }
}

/* Header */

.header > .bg{
    background-color: var(--color_header_bg);
}

.header .line{
    height: 120px;
    transition: ease-out 0.3s;
}

.header .logo{
    display: block;
    flex-shrink: 0;
    font-size: 0;
}

.header .logo img{
    width: auto;
    height: auto;
}

.header .logo svg + .title-store,
.header .logo img + .title-store{
    display: none;
}

.account i{
    color: var(--color_header_highlight);
    margin-right: 8px;
    font-size: 25px;
}

.account [data-logged-user="true"]:not(.tray-hide) + [data-logged-user]{
    display: none;
}

.account div > span{
    -webkit-box-flex: 1; 
    -ms-flex-positive: 1;
    min-height: 17px;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -o-text-overflow: ellipsis;
       text-overflow: ellipsis; 
    -webkit-line-clamp: 1;
    display: -webkit-box;
}

.account span{
    color: var(--color_header_main_font);
    font-size: .875rem;
    font-weight: 500;
}

.account div,
.account a,
.account .login-links span{
    color: var(--color_header_second_font);
    font-size: .81rem;
    transition: ease-out .2s;
    line-height: 15px;
}

.account a:hover{
    opacity: .8;
}

.account .login-links{
    display: flex;
}

.account .login-links span{
    padding: 0 4px;
}

.header .cart-toggle .icon{
    font-size: 25px;
    color: var(--color_header_highlight);
    display: inline-block;
}

.header .cart-toggle .cart-quantity{
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    line-height: 23px;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 400;
    vertical-align: middle;
    color: var(--color_header_bg);
    background-color: var(--color_header_cart_count);
}

.header .cart-toggle{
    margin-left: 2.875rem;
    flex-shrink: 0;
    transition: ease-out .2s;
}

.header .cart-toggle:hover{
    opacity: .8;
}


@media (min-width: 768px){

    .header:not(.fixed) .line{
        border-bottom: 2px solid var(--color_header_details);
    }

    .header-mobile{
        display: none;
    }

    .header-menu{
        display: none;
    }

    .header{
        position: fixed;
        width: 100%;
        z-index: 10;
        top: 0;
        transition: ease-out 0.3s;
    }

    .header .logo{
        margin-right: 1.875rem;
    }

    .header .logo svg,
    .header .logo img{
        max-width: 100%;
        max-height: 74px;
        transition: ease-out 0.3s;
    }

    .header .header-search-wrapper{
        max-width: 600px;
        margin: 0 60px 0 auto;
    }


    

    .header.fixed{
        max-height: 115px;
    }

    .header.fixed .bg{
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
        transition: ease-out 0.3s
    }

    .header.fixed .line{
        height: 84px;
        transition: ease-out 0.3s 0.3s
    }

    .header.fixed .logo svg,
    .header.fixed .logo img{
        max-height: 56px;
        transition: ease-out 0.3s 0.3s
    }    

}

@media (max-width: 767px){

    .header-menu{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-grow: 0;
        flex-shrink: 0;
        width: 20px;
        height: 18px;
        margin-right: 28px;
        cursor: pointer;
    }

    .header-menu div{
        width: 100%;
        height: 2px;
        background-color: var(--color_header_highlight);
    }

    .header .cart-toggle .cart-quantity{
        width: 20px;
        height: 20px;
        font-size: 11px;
        line-height: 20px;
    }

    .header-mobile{
        position: fixed;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 48px;
        padding: 0 0.625rem;
        background-color: var(--color_header_bg);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
        transform: translateY(calc(-100% - 10px));
        transition: ease-out 0.3s;
        z-index: 10;
    }

    .header-mobile.show{
        transform: translateY(0);
    }

    .header-mobile .cart-toggle{
        margin-left: 1.875rem;
        flex-shrink: 0;
    }

    .header-mobile .cart-toggle .icon{
        font-size: 1.5rem;
        height: 28px;
        color: var(--color_header_highlight);
        display: inline-block;
    }

    .header-mobile .cart-toggle .cart-quantity{
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-left: 10px;
        border-radius: 50%;
        line-height: 20px;
        text-align: center;
        font-size: 0.9375rem;
        font-weight: 500;
        vertical-align: middle;
        color: var(--color_header_details);
        background-color: var(--color_header_cart_count);
    }

    .header-mobile .header-search-wrapper .input-search{
        height: 32px;
        padding-left: 5px;
    }

    .header-mobile  .header-search-wrapper .button-search{
        width: 40px;
        font-size: 17px;
    }

    .header-mobile  .header-search-wrapper .button-search .icon{
        display: block;
    }

    .header-mobile:not(.show) .suggestion,
    .header.not-visible .suggestion{
        display: none !important;
    }

    .header-mobile .suggestion{
        top: calc(100% + 8px);
        left: -60px;
        width: calc(100% + 160px);
    }

    .header .line{
        flex-wrap: wrap;
        justify-content: flex-start;
        height: calc(145px + 1.25rem);
        padding-bottom: 1.25rem;
        padding-top: 1.25rem;
    }

    .header .logo{
        order: 1;
        margin: 0 auto;
        max-width: calc(100% - 128px);
    }

    .header .cart-toggle{
        order: 2;
        margin-left: 0;
    }

    .header .header-search-wrapper{
        order: 3;
        margin-top: 1.25rem;
    }

    .header .logo svg,
    .header .logo img{
        max-width: 100%;
        max-height: 45px;
        transition: ease-out 0.3s;
    }

    .header .account{
        display: none;
    }

}


/* Menu */

.nav{
    position: relative;
    z-index: -1;
    background-color: var(--color_header_bg);
    transition: transform 0.3s ease-out 0.3s, background 0.3s ease-out;
}

.nav .container{
    max-width: 1230px;
}

.nav .list,
.nav .list > .first-level{
    position: relative;    
}

.nav .list > li > a,
.nav .list > li > span{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 72px;
    padding: 0 12px;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    color: var(--color_header_main_font);
    z-index: 2;
    transition: ease-out 0.2s;
}

.nav .list > .first-level.sub > a .name::after,
.nav-mobile .first-level > li.sub > a::after{
    content: "\ea01";
    font-family: "go" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    height: 14px;
    font-size: 0.75rem;
    margin-left: 0.3125rem;
    vertical-align: middle;
    transition: ease-out 0.2s;    
}

.nav-mobile .first-level > li.sub > a::after{
    position: absolute;
    right: 15px;
}

.nav .list .second-level{
    position: absolute;
    top: calc(100% - 40px);
    min-width: 275px;
    width: auto;
    padding: 2.0625rem 2.5rem;
    opacity: 0;
    visibility: hidden;
    background-color: var(--color_header_bg);
    border-radius: 0 0 4px 4px;
    transform: translateY(40px);
    border-top: 3px solid var(--color_header_highlight);
    overflow-y: scroll;
    transition: ease-out 0.2s;
    box-shadow: 0 4px 8px rgb(0 0 0 / 6%);
}

.nav .first-level:hover .second-level{
    opacity: 1;
    visibility: visible;
}

.nav .list .second-level{
    scrollbar-color: var(--color_header_highlight) var(--color_header_bg);
    scrollbar-width: thin
}
.nav .list .second-level::-webkit-scrollbar{
    width: 6px;
    background: var(--color_header_bg);
}
.nav .list .second-level::-webkit-scrollbar-track{
    background: var(--color_header_bg);
}
.nav .list .second-level::-webkit-scrollbar-thumb{
    background: var(--color_header_highlight);
    border-radius: 5px;
}

.nav .first-level:nth-child(n+5) .second-level,
.nav .first-level:last-child .second-level{
    left: auto;
    right: 0;
}

.nav .third-level{
    padding-left: 1.10rem;
}

.nav .first-level:hover > a,
.nav .list > li.sub .second-level > li:hover > a,
.nav .list > li.sub .third-level > li:hover > a,
.nav .list > li.sub .third-level > li > ul > li:hover > a{
    color: var(--color_primary);
    opacity: 1;
}

.fixed .nav{
    transform: translateY(-115%);
    transition: transform 0.3s ease-out, background 0s ease-out 0.3s;
}

.fixed .nav .list > li > a .icon{
    max-height: 0;
    margin: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    transition: 0.2s ease-out 0.2s;
}

.fixed .nav.show-nav{
    transform: translateY(0);
    box-shadow: 0 1px 5px rgb(0 0 0 / 6%);
}

@media (max-width: 1000px){

    .nav .list > li > a{
        font-size: 1rem;
        padding: 0 0.625rem;
    }

}

@media (max-width: 900px){

    .nav .list > li > a{
        font-size: 0.875rem;
        padding: 0;
    }

}

@media (max-width: 767px){
    .nav{
        display: none;
    }
}


/* Submenu */

.nav .list > li.sub > .sub-list > li a{
    display: block;
    font-size: 0.875rem;
    transition: ease-out 0.2s;
    color: var(--color_header_second_font);
}

.nav .second-level > li a{
    padding: 0.4375rem 0;
    font-weight: 500;
}

.nav .third-level > li a{
    padding: 0.10rem 0;
    font-weight: 400;
}

.nav .second-level > a{
    font-size: 1.12rem;
    font-weight: 500;
    line-height: 1.31rem;
    color: var(--color_header_highlight);
    margin-bottom: 4px;
    display: block;
}

/* Menu Mobile */

.menu-mobile{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 90%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    padding: 1.875rem 0.625rem;
    background-color: var(--color_header_bg);
    transform: translateX(calc(-100% - 10px));
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.16);
    z-index: 20;
    transition: ease-out 0.3s;
}

.menu-mobile.show{
    transform: translateX(0);
}

.menu-mobile .close-box{
    top: 33px;
    right: 20px;
    color: var(--color_header_main_font);
}

.menu-mobile .header-menu{
    margin-right: 1.25rem;
    cursor: default;
}

.menu-mobile .block-title{
    width: 100%;
    font-size: 1.125rem;
    font-weight: 500;
    padding-right: 3.125rem;
    color: var(--color_header_main_font);
}

.menu-mobile .nav-mobile-wrapper{
    flex-grow: 1;
    height: calc(100% - 180px);
    margin: 1.875rem 0;
    padding: 1.875rem 0;
    overflow: auto;
    border-top: solid 2px var(--color_header_details);
    border-bottom: solid 2px var(--color_header_details);
}

/* Nav Mobile */

.nav-mobile{
    overflow-y: auto;
    height: 100%;
    overflow-x: hidden;
}

.nav-mobile{
    scrollbar-color: var(--color_header_highlight) var(--color_header_details);
    scrollbar-width: thin
}
.nav-mobile::-webkit-scrollbar{
    width: 6px;
    background: var(--color_header_details);
}
.nav-mobile::-webkit-scrollbar-track{
    background: var(--color_header_details);
}
.nav-mobile::-webkit-scrollbar-thumb{
    background: var(--color_header_highlight);
    border-radius: 5px;
}

.nav-mobile .list li a{
    position: relative;
    display: flex;
    align-items: center;
    padding: 0.3125rem 0;
    color: var(--color_header_main_font);
}

.nav-mobile li.sub > .second-level,
.nav-mobile li.sub > .third-level{
    padding-left: 20px;
}

.nav-mobile li.sub > .second-level{
    display: none;
}

.nav-mobile li.sub.show > .second-level{
    display: block;
}

.nav-mobile li.sub.show > a::after{
    transform: rotate(-180deg);
}


/* Banner Home */
.item-image{
    position: relative;
    z-index: 1;
    overflow: hidden;
    display: block;
}

.item-image::after{
    content: '';
    display: block;
    padding-bottom: var(--padding);
}

.item-image img{
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: opacity ease-out .2s;
}

.item-image img.swiper-lazy{
    opacity: 0;
}

.item-image img.swiper-lazy.swiper-lazy-loaded{
    opacity: 1;
}

.banner-home{
    position: relative;
}

.banner-home .item a{
    display: block;
}

.banner-home .item img{
    display: block;
    width: 100%;
}

.banner-home .swiper-container .dots{
    margin: 0;
    position: absolute;
    bottom: 28px;
    z-index: 5;
}

@media (max-width: 767px) {

    .banner-home .item img{
        max-height: 620px;
    }

}

/* Banner Grid */

.banners-grid{
    margin: 1.875rem 0 2.5rem 0;
}

.banners-grid .item{
    width: 100%;
}

.banners-grid.two .item{
    width: calc(50% - 15px);
}

.banners-grid a{
    display: block;
}

.banners-grid .item a,
.banners-grid .item span{
    display: block;
    width: 100%;
    transition: ease-out 0.3s;
    border-radius: 2px;
}

.banners-grid a:hover{
    opacity: 0.9;
    transform: scale(1.03);
}

@media (max-width: 767px){

    .banners-grid{
        flex-direction: column;
        margin: 0.625rem 0;
    }

    .banners-grid .item,
    .banners-grid.two .item{
        width: 100%;
    }

    .banners-grid .item + .item{
        margin-top: 10px;
    }

}


/* Banner Line */

.banner-line{
    margin: 2.5rem 0;
}

.banner-line img{
    transition: ease-out 0.3s;
    border-radius: 2px;
}

.banner-line a:hover img{
    transform: scale(1.03);
}

/* Product rating */

.product-rating{
    font-size: 1rem;
    margin: 0.75rem 0;
}

.product-rating .total{
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.3125rem;
    color: var(--color_primary_light);
    transition: ease-out 0.2s;
}

.product-form .product-rating{
    flex-wrap: wrap;
}

.product-form .product-rating .total{
    width: 100%;
    text-align: center;
}

.product-rating .total:hover{
    opacity: .8;
}

.product-rating .icon{
    background: url(../img/empty-star.svg) no-repeat;
    width: .938em;
    height: .938em;
    background-size: 100%;
    display: block;
}

.product-rating .icon.active{
    background-image: url(../img/star.svg);
}

.product-rating .icon:not(:last-child){
    margin-right: 0.3125rem;
}


/* Product buttons*/
.actions .product-button {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
    height: 44px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    transition: ease-out 0.2s;
}

.actions .product-button:hover{
    opacity: .8;
}


/* Product Tags */

.product-tags .tag{
    width: 53px;
    height: 42px;
    font-size: 0.75rem;
    font-weight: 700;
    /* border-radius: 2px; */
    /* padding: 0.25rem 0.3125rem; */
    line-height: 1.4;
    text-align: center;
    word-wrap: break-word;
    text-transform: uppercase;
    overflow: hidden;
}

.product-tags .tag img{
    width: auto;
    height: auto;
    max-width: 100%;
}

.product-tags .tag + .tag{
    margin-left: 5px;
}



/* Product box */

.product{
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    color: var(--color_font_medium);
    background-color: #fff;
    border-radius: 2px;
    overflow: hidden;
    z-index: 3;
    transition: ease-in-out 0.2s;
    border: 1px solid var(--color_gray_medium);
}

.product:hover{
    box-shadow: 0 4px 8px var(--color_gray_dark);
}

.product .image{
    position: relative;
}

.product .space-image{
    display: block;
    position: relative;
    overflow: hidden;
}

.product .space-image::after{
    content: '';
    display: block;
    padding-bottom: 100%;
}

.product .space-image img{
    display: block;
    max-width: 100%;
    width: auto !important;
    max-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    width: 100%;
    height: 100%;
    object-fit: scale-down;
    object-position: center;
}    

.product .space-image img:not(.loaded){
    width: 100%;
}

.product .product-tags{
    position: absolute;
    left: 1.25rem;
    width: calc(100% - 2.5rem);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.product .product-tags[data-tags-count="1"],
.product .product-tags[data-tags-count="2"]{
    bottom: -13px;
}

.product .product-tags[data-tags-count="3"],
.product .product-tags[data-tags-count="4"]{
    bottom: -25px;
}

.product .product-tags .tag{
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(50% - 0.125rem);
    height: auto;
}

.product .product-tags .tag:only-child{
    width: 100%;
}

.product .product-tags .tag + .tag{
    margin: 0;
}

.product .product-tags .tag:nth-child(n+3){
    margin-top: 4px;
}

.product .product-tags[data-tags-count="3"] .tag:nth-child(1){
    width: 100%;
}

.product .product-tags[data-tags-count="3"] .tag:nth-child(n+2){
    margin-top: 4px;
}

.product .product-tags-circle{
    position: absolute;
    top: 0.25rem;
    left: 1.25rem;
    display: flex;
    flex-direction: column;
}

.product .product-tags-circle .tag-circle{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
}

.product .product-tags-circle .tag-circle + .tag-circle{
    margin-top: 0.25rem;
}

.product .product-tags-circle .tag-circle.free-shipping{
    font-size: 0.875rem;
    background-color: var(--color_bg_seal);
    color: var(--color_seal);
}

.product .product-tags-circle .tag-circle.progressive-discount{
    background-color: var(--color_bg_seal);
    color: var(--color_seal);
}

.product .product-tags-circle .tag-circle.discount{
    flex-direction: column;
    font-size: 0.75rem;
    line-height: 1;
    background-color: var(--color_bg_seal);
    color: var(--color_seal);
}

.product .product-tags-circle .tag-circle.discount .discount-value{
    font-weight: 700;
    text-align: center;
}

@media (min-width: 768px){
    .product .product-tags-circle .tag-circle.free-shipping{
        display: none;
    }
}

@media (max-width: 767px){

    .product .product-tags{
        left: 0;
        width: 100%;
    }

    .product .product-tags-circle{
        left: 0.25rem;
    }

}

@media (max-width: 440px){

    .product .product-tags-circle .tag-circle{
        width: 2rem;
        height: 2rem;
    }

}

.product .product-info{
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 2rem 1.25rem 1.875rem 1.25rem;
}

.product .product-info .product-name{
    color: var(--color_font_dark);
    flex-grow: 1;
    max-height: 45px;
    font-size: 1rem;
    font-weight: 600;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product .product-rating{
    height: 15px;
    overflow: hidden;
    transition: ease-out 0.2s;
}

/* Iconografia */
#iconografia .icones{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.25rem 0;
} 

#iconografia .icones img{
    max-width: 30px;
    margin-right: 0.25rem;
}  

/* Product price */

.product .product-price{
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    min-height: 70px;
    transition: ease-out 0.2s;
    color: var(--color_font_light);
}

.product .product-price .product-has-variants{
    font-size: 0.75rem;
}

.product .product-price .price .precode,
.product .product-price .price .oculta_destaque,
.product .product-price .price .old-price{
    font-size: 0.75rem;
    text-decoration: line-through;
}

.product .product-price .price .current-price{
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color_primary_medium);
}

.product .product-price .product-installments{
    font-size: .87rem;
    font-weight: 500;
    display: block;
}

/* Product not available or under request */

.product .product-price .product-message{
    font-size: 1.375rem;
    font-weight: 600;
    color: var(--color_font_light);
}

.product .product-price .product-message .notify-me{
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.3125rem;
    color: var(--color_font_light);
}

.product .product-price .product-message .notify-me .icon{
    font-size: 0.5rem;
}

.product .actions{
    bottom: 20px;
    position: absolute;
    opacity: 0;
    visibility: hidden;
    transition: ease-in-out .2s;
    width: 100%;
    left: 0;
    padding: 0 1.25rem;
}

@media (min-width: 768px){

    .product.show-down:hover {
        padding-bottom: 35px;
        margin-bottom: -35px;
        transition-delay: 0s;
    }

    .product.show-down:hover .product-rating{
        opacity: 0;
    }

    .product.show-down:hover .product-rating + .product-price{
        transform: translateY(-25px);
    }

    .product:hover .actions {
        opacity: 1;
        visibility: visible;
        transition-delay: 0.2s;
    }

}

@media (max-width: 870px){

    .product .tags{
        display: none;
    }

}

@media (max-width: 767px){

    .product .product-info{
        padding: 2rem 0.625rem 1.25rem 0.625rem;
    }

    .product .product-info .product-name{
        font-size: 0.75rem;
        max-height: 33px;
    }

    .product .product-price .price .current-price{
        font-size: 1rem;
    }

    .product .product-price .product-message{
        font-size: 0.875rem;
    }

    .product .product-price .product-message .notify-me{
        font-size: 0.75rem;
    }

    .product .actions{
        position: initial;
        padding: 0 0.625rem 1.25rem 0.625rem;
        opacity: 1;
        visibility: visible;
    }

    .product .product-price .product-message .notify-me .icon{
        display: none;
    }    

}

@media (max-width: 575px){
    .product .image {
        position: relative;
    }
}

@media (max-width: 450px){
    .product .product-button .icon{
        font-size: 1.25rem;
        height: 22px;
    }

    .product .quantity-buttons{
        display: none;
    }
}


/* Showcase */

.section-showcase{
    position: relative;
    padding: 2.5rem 0;
}

.section-showcase.z-index{
    z-index: 5;
}

.section-showcase .swiper-container{
    padding-bottom: 40px;
    margin-bottom: -40px;
}

@media(max-width: 575px){
    .section-showcase .container{
        padding: 0 0.3125rem;
    }
}


/* Product Listing */

.list-product {
    margin: 0 -.625rem -2.5rem;
}

.list-product .item{
    display: flex;
    height: auto;
    width: 25%;
    margin-bottom: 2.5rem;
    box-sizing: border-box;
    padding: 0 0.625rem;
}

@media (max-width: 1080px){

    .list-product:not(.swiper-wrapper) .item{
        width: 33.33333%;
    }

}

@media (max-width: 800px) {

    .list-product:not(.swiper-wrapper) .item {
        width: 50%;
    }

}

@media (max-width: 767px) {
    .list-product{
        margin-left: 0;
        margin-right: 0;
    }

    .list-product .item {
        padding: 0 5px;
    }
}

/* News home */
.section-news .botao-commerce{
    max-width: 196px;
    margin: 25px auto 0;
}

/* Blog posts */

.box-noticia{
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    width: 100%;
    height: 100%;
    align-items: center;
    border: 1.5px solid var(--color_gray_medium);
    transition: ease-out 0.2s;
    border-radius: 8px;
}

.box-noticia:hover {
    box-shadow: 0 4px 8px var(--color_gray_medium);
}

html.page-home .box-noticia{
    border: 0 none;
    padding: 0 10px;
}

html.page-home .box-noticia:hover {
    box-shadow: none;
}

html.page-home .box-noticia > div{
    border: 1.5px solid var(--color_gray_medium);
    border-radius: 8px;
}

html.page-home .box-noticia:hover > div{
    box-shadow: 0 4px 8px var(--color_gray_medium);
}

.box-noticia #noticia_imagem{
    width: 100%;
    height: 240px;
    overflow: hidden;
}

.box-noticia #noticia_imagem a{
    display: block;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.box-noticia #noticia_imagem img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: ease-out 0.2s;
}

.box-noticia #noticia_imagem img.swiper-lazy-loaded,
.box-noticia #noticia_imagem img.loaded{
    opacity: 1;
}

.box-noticia #noticia_dados{
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    width: 100%;
    padding: 1.25rem 2.5rem;
    position: relative;
    text-align: center;
}

.box-noticia #noticia_dados h3{
    font-size: 0;
    font-weight: 700;
    margin-top: 5px;
    flex-grow: 1;
}

.box-noticia #noticia_dados h3 a{
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    text-align: center;
    color: var(--color_primary_medium);
    line-height: 1.15;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 2;
    min-height: 1.32;
    transition: ease-out 0.2s;
}

.box-noticia #noticia_dados h3 a:hover{
    opacity: .8;
}

.box-noticia #noticia_dados p{
    max-height: 58px;
    font-size: .875rem;
    line-height: 1.36;
    flex-grow: 1;
    color: var(--color_font_light);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 3;
}

.box-noticia #noticia_dados .button-show{
    background: var(--color_primary_medium);
    color: var(--color_font_inverted);
    display: block;
    margin: 25px auto 0 auto;
    width: 100%;
    max-width: 196px;
    height: 41px;
    border-radius: 2px;
    line-height: 41px;
    text-align: center;
    font-size: .87rem;
    font-weight: 700;
    text-transform: uppercase;
    transition: ease-out 0.2s;
}

.box-noticia:hover #noticia_imagem img{
    transform: scale(1.1);
}

.box-noticia #noticia_dados .button-show:hover{
    opacity: .8;
}

@media (max-width: 767px){

    .noticias{
        margin: 0;
    }

    .box-noticia #noticia_dados{
        padding: 1.25rem;
    }

}

/* Review Blocks */

.dep_item {
    display: flex;
    padding: 0 10px;
    width: 33.333333%;
}

.dep_item.swiper-slide{
    height: auto;
}

.dep_item a{
    display: flex;
    flex-grow: 1;
    position: relative;
    background-color: var(--color_gray_medium);
    border-radius: 8px;
}

.dep_dados{
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    text-align: center;
    padding: 1.5625rem 2.5rem;
    z-index: 0;
    color: var(--color_font_dark);
}

.dep_dados .dep_img{
    position: absolute;
    top: 33px;
    left: 20px;
    bottom: 0;
    z-index: -1;
    opacity: 0.7;
}

.dep_dados .dep_msg{
    flex-grow: 1;
    font-weight: 400;
    order: -2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    line-height: 1.25;
    text-align: center;
    transition: ease-in-out .3s;
}

.dep_dados .dep_nome{
    font-size:  1rem;
    font-weight: 500;
    line-height: 1.5;
    margin: 0.625rem 0 0 0;
}

.dep_nota{
    display: none;
}

.dep_dados .dep_data,
.dep_dados .dep_nome span,
.dep_dados .dep_msg span{
    display: none;
}

.dep_link{
    display: none;
}

/* Reviews home */

.section-avaliacoes{
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.4s;
    padding: 2.5rem 0;
}

.section-avaliacoes .swiper-container .prev, 
.section-avaliacoes .swiper-container .next{
    top: 28%;
    bottom: auto;
}

.section-avaliacoes.show{
    opacity: 1;
    visibility: visible;
}

.section-avaliacoes .swiper-container{
    margin: 0 -10px;
}


@media (max-width: 767px){

    .section-avaliacoes{
        padding: 2rem 0;
    }

}


/* Breadcrumbs */

.breadcrumb{
    width: 100%;
    margin: 0 0 1.875rem 0;
    font-size: 0.75rem;
    font-style: italic;
    color: var(--color_font_light);
}

.breadcrumb a{
    font-weight: 400;
    transition: color ease-out 0.3s;
}

.breadcrumb a:hover{
    opacity: .8;
}

.breadcrumb .breadcrumb-item:not(:last-child){
    padding-right: 25px;
    position: relative;
}

.breadcrumb .breadcrumb-item:not(:last-child)::after{
    content: '/';
    display: block;
    position: absolute;
    top: 0;
    right: 10px;
}


/* Pagination */

.pagination .page{
    margin: 0 0.1875rem;
}

.pagination .page:first-child{
    margin-left: 0;
}

.pagination .page:last-child{
    margin-right: 0;
}

.pagination .page a,
.pagination .page.current{
    display: block;
    min-width: 44px;
    height: 38px;
    font-size: 1rem;
    font-weight: 600;
    line-height: 38px;
    text-align: center;
    background-color: var(--color_gray_medium);
    color: var(--color_font_light);
    border-radius: 2px;
    transition: ease-out 0.2s;
}

.pagination .page.first a,
.pagination .page.last a{
    padding: 0 1.25rem;
}

.pagination .page.current{
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    font-size: 0.875rem;
    font-weight: 700;
    text-align: center;
    line-height: 38px;
}

.pagination .page a:hover {
    opacity: .8;
}

.pagination .page .icon{
    font-size: 0.625rem;
}

@media (min-width: 768px){

    .pagination .page.first .icons,
    .pagination .page.last .icons{
        display: none;
    }

    .pagination .page.first .text,
    .pagination .page.last .text{
        display: block;
    }

}

@media (max-width: 767px){

    .pagination .page.first a,
    .pagination .page.last a{
        padding: 0;
    }

    .pagination .page.first .icons,
    .pagination .page.last .icons{
        display: block;
    }

    .pagination .page .icon{
        font-size: 0.625rem;
        width: 0.5rem;
        display: inline-block;
    }

    .pagination .page.first .icons .icon:last-child{
        margin-right: 0.1875rem;
    }


    .pagination .page.first .text,
    .pagination .page.last .text{
        display: none;
    }

}


/* Catalog | Category page */

.catalog-cols .sidebar-category{
    width: 285px;
    padding-right: 2.5rem;
}

.catalog-cols .col-content{
    width: 100%;
}

.catalog-cols .col-content .catalog-empty{
    font-size: 1rem;
    text-align: center;
}

.catalog-header .catalog-info{
    margin-bottom: 2.5rem;
}

.catalog-header .catalog-info .catalog-name{
    position: relative;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
    max-width: 365px;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
}

.catalog-header .catalog-info .catalog-name .search-results{
    font-size: 12px;
    font-weight: 400;
    display: block;
    color: var(--color_font_light);
}

.catalog-header .banner + .catalog-info{
    margin-top: 1.875rem;
}

.catalog-header .banner img{
    display: block;
    width: 100%;
    height: 100%;
    max-height: 250px;
    object-fit: cover;
    transition: ease-out 0.2s;
}

.catalog-header .banner a:hover img{
    opacity: 0.9;
}

.catalog-header .catalog-info .catalogo-form-filtros{
    font-size: .75rem;
}

.catalog-header .catalog-info .catalogo-form-filtros label:not(:first-child){
    font-size: 0;
}

.catalog-header .catalog-info .catalogo-form-filtros label.filtro-ordem + label{
    display: none;
}

.catalog-header .catalog-info .catalogo-form-filtros select.select{
    height: 50px;
    min-width: 207px;
    padding-left: 0.875rem;
    font-size: 1rem;
    font-weight: 600;
}

.catalog-header .description{
    margin: -0.9375rem 0 2.5rem 0;
}

.catalog-content .showcase-catalog .list-product{
    margin: 0 -10px;
}

.catalog-footer .results{
    font-size: 0.875rem;
    font-weight: 500;
}

@media (min-width: 768px){

    .catalog-header .catalog-info .button-filter-mobile{
        display: none;
    }

    .catalog-header .catalog-info .sort-mobile{
        display: none;
    }

    .catalog-header .catalogo-form-filtros label:not(:first-child)::before{
        content: 'Ordenar por:';
        font-size: 0.875rem;
        font-weight: 500;
        padding-right: 1.25rem;
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header{
        display: none;
    }

    .catalog-cols .sidebar-category + .col-content{
        width: calc(100% - 285px);
    }

    .catalog-cols .sidebar-category + .col-content .showcase-catalog .list-product .item{
        width: 33.333333%;
    }

    .button-filter{
        display: none;
    }

}

@media (min-width: 768px) and (max-width: 960px){
    .catalog-cols .sidebar-category + .col-content .showcase-catalog .list-product .item{
        width: 50%;
    }
}

@media (max-width: 1024px) {

    .catalog-footer{
        flex-direction: column-reverse;
    }

    .catalog-footer .results{
        width: 100%;
        text-align: center;
        margin-top: 15px;
    }

}

@media (max-width: 767px) {

    .catalog-cols .sidebar-category {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 90%;
        padding: 1.875rem 0.625rem;
        background-color: #fff;
        transform: translateX(calc(-100% - 10px));
        transition: ease-out 0.3s;
        z-index: 20;
    }

    .catalog-cols .sidebar-category.show{
        transform: translateX(0);
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .close-box{
        top: 35px;
        right: 10px;
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .block-title{
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--color_font_dark);
        margin-bottom: 1.875rem;
        padding: 0 3.125rem 1.25rem 0;
        border-bottom: solid 2px var(--color_gray_dark);
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .block-title .icon{
        font-size: 1.75rem;
        height: 2rem;
        margin-right: 0.9375rem;
        color: var(--color_primary_medium);
    }

    .catalog-cols .sidebar-category .smart-filter{
        background-color: transparent;
        padding: 0;
        overflow: auto;
        height: calc(100% - 80px);
        scrollbar-color: var(--color_primary_medium) var(--color_gray_medium);
        scrollbar-width: thin
    }
    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar{
        width: 6px;
        background: var(--color_gray_medium);
    }
    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar-track{
        background: var(--color_gray_medium);
    }
    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar-thumb{
        background: var(--color_primary_medium);
        border-radius: 5px;
    }

    .catalog-cols .sidebar-category .smart-filter .filters{
        overflow: hidden;
    }

    .catalog-header .catalog-info .catalog-name{
        margin-top: -0.625rem;
        font-size: 1.25rem;
        order: 2;
        text-align: center;
        max-width: calc(100% - 170px);
    }

    .catalog-header .catalog-info .catalog-name::after{
        width: 100%;
        left: 50%;
        transform: translateX(-50%);
    }

    .catalog-header .catalog-info .system-filter{
        order: 3;
    }

    .catalog-header .catalog-info .button-filter-mobile,
    .catalog-header .sort-mobile .sort-mobile-button{
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex-shrink: 0;
        flex-grow: 0;
        align-items: center;
        width: 70px;
        height: 70px;
        font-size: 0.75rem;
        text-align: center;
        padding: 0.625rem 0.5rem 0.3125rem 0.5rem;
        background-color: var(--color_gray_medium);
        color: var(--color_font_light);
        border-radius: 2px;
        cursor: pointer;
    }

    .catalog-header .catalog-info .button-filter-mobile .icon,
    .catalog-header .sort-mobile .sort-mobile-button .icon{
        display: block;
        font-size: 1.25rem;
    }

    .catalog-header .catalog-info .button-filter-mobile{
        margin-right: 0.9375rem;
    }

    .catalog-header .sort-mobile .sort-mobile-button{
        margin-left: 0.9375rem;
    }

    .catalog-header .sort-mobile .sort-panel{
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 90%;
        max-width: 400px;
        padding: 1.875rem 0.625rem;
        background-color: #fff;
        transform: translateX(calc(100% + 10px));
        transition: ease-out 0.3s;
        z-index: 20;
    }

    .catalog-header .sort-mobile .sort-panel.show{
        transform: translateX(0);
    }

    .catalog-header .sort-mobile .sort-panel .close-box{
        top: 35px;
        right: 10px;
    }

    .catalog-header .sort-mobile .sort-panel .block-title{
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--color_font_dark);
        margin-bottom: 1.875rem;
        padding: 0 3.125rem 1.25rem 0;
        border-bottom: solid 2px var(--color_gray_dark);
    }

    .catalog-header .sort-mobile .sort-panel .block-title .icon{
        font-size: 1.75rem;
        height: 2rem;
        margin-right: 0.9375rem;
        color: var(--color_primary_medium);
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li{
        position: relative;
        padding: 10px 0;
        cursor: pointer;
        transition: ease-out 0.2s;
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li:hover{
        color: var(--color_primary_medium);
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li.active{
        color: var(--color_primary_medium);
        font-weight: 500;
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li.active::after{
        position: absolute;
        content: '';
        top: 13px;
        right: 10px;
        display: block;
        width: 1rem;
        height: 1rem;
        background: var(--color_primary_medium) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e") no-repeat 50% 50%;
        border-radius: 50%;
    }

    .catalog-header .catalog-info .catalogo-form-filtros{
        display: none;
    }   

}


/* Smart Filter */

.smart-filter{
    padding: .5rem 0.9375rem;
}

.smart-filter .filter-block{
    margin-bottom: 1.875rem
}

.smart-filter .filter-block:last-child ul{
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.smart-filter .filter-title{
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    padding: 0.625rem 0;
    cursor: pointer;
    border-bottom: transparent;
    transition: 0.1s ease-out 0.3s;
}

.smart-filter .filter-block.closed .filter-title{
    border-bottom: solid 4px var(--color_gray_dark);
}

.smart-filter .filter-list{
    margin-bottom: 1.875rem;
    padding-bottom: 1.375rem;
    border-bottom: solid 4px var(--color_gray_dark);
    transition: border-bottom 0.1s ease-out 0.2s;
}

.smart-filter .filter-block.closed .filter-list{
    border-bottom: transparent;
    transition: border-bottom ease-out 0.2s;
}

.smart-filter .filter-list .filter-item{
    padding: 0.5rem 0;
}

.smart-filter .filter-checkbox{
    position: relative;
    display: block;
}

.smart-filter .filter-checkbox .filter-input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0;
}

.smart-filter .filter-checkbox .filter-label {
    position: relative;
    display: block;
    padding-left: 1.75rem;
    cursor: pointer;
}

.smart-filter .filter-checkbox .filter-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color_font_light);
    vertical-align: top;
}

.smart-filter .filter-checkbox .filter-count{
    font-size: 0.75rem;
    vertical-align: text-top;
}

.smart-filter .filter-checkbox .filter-label::before {
    position: absolute;
    content: '';
    top: 0.125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    background-color: #fff;
    border: solid 1px var(--color_gray_dark);
    border-radius: 2px;
    transition: ease-in-out 0.2s;
    box-sizing: border-box;
}

.smart-filter .filter-checkbox .filter-label::after {
    position: absolute;
    content: '';
    top: 0.125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    background: no-repeat 50%/50% 50%;
}

.smart-filter .filter-checkbox .filter-input:checked ~ .filter-label::before {
    color: var(--color_font_medium);
    border-color: var(--color_primary_medium);
    background-color: var(--color_primary_medium);
}

.smart-filter .filter-checkbox .filter-input:checked ~ .filter-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

.smart-filter .filter-button{
    width: 100%;
    margin: 0;
    padding: 10px;
    font-size: 0.875rem;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    border-radius: 4px;
    transition: ease-out 0.2s;
    font-family: var(--font_family);
}

.smart-filter .filter-button:hover{
    opacity: .8;
}

@media (min-width: 767px){

    .smart-filter .filter-list{
        max-height: 345px;
        overflow-y: auto;
        scrollbar-color: var(--color_primary_medium) var(--color_gray_dark);
        scrollbar-width: thin
    }
    .smart-filter .filter-list::-webkit-scrollbar{
        width: 6px;
        background: var(--color_gray_dark);
    }
    .smart-filter .filter-list::-webkit-scrollbar-track{
        background: var(--color_gray_dark);
    }
    .smart-filter .filter-list::-webkit-scrollbar-thumb{
        background: var(--color_primary_medium);
        border-radius: 5px;
    }

}


/* Product Page */

.progressive-discount-banners{
    margin-bottom: 1.875rem;
}

.progressive-discount-banners img{
    max-width: 100%;
    margin: 0 auto;
    display: block;
}

.product-wrapper{
    margin-bottom: 5rem;
}

.product-wrapper .product-box{
    display: flex;
}

.product-wrapper .product-box .product-video{
    position: absolute;
    top: 10px;
    left: 20px;
    display: flex;
    align-items: center;
    padding: 0.3125rem 0;
    color: #f00;
    border-radius: 2px;
    cursor: pointer;
    z-index: 5;
    transition: ease-out 0.2s;
}

.product-wrapper .product-box .product-video .text{
    width: 0;
    margin-left: 0.3125rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    transition: ease-out 0.2s;
}

.product-wrapper .product-box .product-video .icon{
    font-size: 30px;
    height: 30px;
}

.product-wrapper .product-box .product-video .icon::before{
    vertical-align: top;
}
.product-wrapper .product-box .product-video .icon::after{
    content: '';
    background: #fff;
    position: absolute;
    width: 12px;
    height: 12px;
    top: 14px;
    left: 9px;
    z-index: -1;
}

.product-wrapper .product-box .product-video:hover .text{
    opacity: 1;
    width: 70px;
}

.product-wrapper .product-box .product-gallery{
    width: calc(100% - 470px);
    padding-right: 2.5rem;
    position: relative;
    display: flex;
    flex-direction: column;
}

.tag-discount{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
    font-size: .75rem;
    line-height: 1;
    background-color: var(--color_bg_seal);
    color: var(--color_seal);
    position: absolute;
    top: 0.25rem;
    right: 1.25rem;
    z-index: 2;
    font-weight: 700;
    text-align: center;
}

.product-wrapper .product-gallery .product-images{
    width: 100%;
    height: 100%;
    max-height: 588px;
    border: solid 1px var(--color_gray_medium);;
    border-radius: 6px;
}

.product-wrapper .product-gallery .product-images .image{
    cursor: move;
}

.product-wrapper .product-gallery .product-images .zoom{
    width: 100%;
    height: 100%;
    position: relative;
}

.product-wrapper .product-gallery .product-images .zoom::before{
    content: '';
    display: block;
    padding-bottom: 100%;
}

.product-wrapper .product-gallery .product-images img{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-wrapper .product-gallery .product-thumbs{
    margin: 1.875rem 0 0 0;
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.3s;
}

.product-wrapper .product-gallery .product-thumbs.show{
    opacity: 1;
    visibility: visible;
}

.product-wrapper .product-gallery .product-thumbs .thumbs-list{
    padding: 0.0625rem;
    margin: 0 3.125rem;
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide{
    padding: 0 0.3125rem;
    width: 20%;
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide .thumb{
    border: solid 1px transparent;
    cursor: pointer;
    border-radius: 2px;
    opacity: 0.7;
    transition: ease-out 0.2s;
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide .thumb:hover,
.product-wrapper .product-gallery .product-thumbs .swiper-slide.swiper-slide-thumb-active .thumb{
    opacity: 1;
    border: solid 1px var(--color_primary_medium);
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide img{
    display: block;
    margin: 0 auto;
    max-height: 90px;
    max-width: 90px;
}

.product-wrapper .product-gallery .product-thumbs:not(.show-arrows) .prev,
.product-wrapper .product-gallery .product-thumbs:not(.show-arrows) .next{
    display: none;
}


.product-wrapper .product-gallery .product-thumbs.show-arrows .prev,
.product-wrapper .product-gallery .product-thumbs.show-arrows .next{
    transition: ease-out 0.2s;
}

.product-wrapper .product-gallery .product-thumbs.show-arrows .prev.swiper-button-disabled,
.product-wrapper .product-gallery .product-thumbs.show-arrows .next.swiper-button-disabled{
    visibility: visible;
    opacity: 0.7;
    cursor: default;
}

.product-wrapper .product-box .product-form{
    position: relative;
    width: 470px;
    padding: 2.5rem 1.875rem;
    border: solid 1px var(--color_gray_medium);
    border-radius: 6px;
}

.product-wrapper .product-box .product-form *{
    max-width: 100%;
    word-wrap: break-word;
}

.product-wrapper .product-form .product-tags{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.product-wrapper .product-form .product-tags .tag{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30%;
    height: auto;
    margin: 5px;
}

.product-wrapper .product-form .product-tags + .product-name{
    margin-top: 1.25rem;
}

.product-wrapper .product-form .product-name{
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color_font_dark);
    text-align: center;
}

.product-wrapper .product-form .product-main-info{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 0.3125rem;
    color: var(--color_font_light);
}

.product-wrapper .product-form .product-sku,
.product-wrapper .product-form .product-brand{
    margin: 0 0.3125rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.product-wrapper .product-form .product-sku + .wishlist,
.product-wrapper .product-form .product-brand + .wishlist{
    margin-left: 0.625rem;
}

.product-wrapper .product-form .product-rating{
    justify-content: center;
    margin: 1.25rem 0;
}

.product-wrapper .product-form .product-info{
    margin-top: 1.25rem;
    font-size: 0.75rem;
    text-align: center;
}

.product-wrapper .product-form .product-info + .product-info{
    margin-top: 0;
}

.product-wrapper .product-form .product-info .bold{
    font-weight: 500;
}

.product-wrapper .product-form .product-additional-message{
    font-size: 0.875rem;
    margin-bottom: 1.25rem;
    text-align: center;
}

.product-wrapper .product-form .product-name + .product-additional-message{
    margin-top: 1.25rem;
}

.product-wrapper .product-form .product-additional-message br{
    display: none;
}

/* Product variants */

.product-wrapper .product-form .product-variants{
    margin: 1.25rem 0 2.5rem 0;
}

.product-wrapper .product-form .variant-error{
    margin-top: 1.25rem;
}

.product-wrapper .product-form .texto_variacao{
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.625rem;
    color: var(--color_font_dark);
    text-align: center;
}

.product-wrapper .product-form .texto_variacao h2{
    font-weight: inherit;
    display: inline;
    font-size: inherit;
}

.product-wrapper .product-form .texto_variacao h2::after{
    content: ':';
}

.product-wrapper .product-form .texto_variacao span{
    color: var(--color_font_light);
}

.product-wrapper .product-form .cor_variacao ul,
.product-wrapper .product-form #opcoes0 ul{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.product-wrapper .product-form .cor_variacao ul:not(.listaVarMultipla),
.product-wrapper .product-form #opcoes0 ul:not(.listaVarMultipla){
    margin-left: -5px;
}

.product-wrapper .product-form #opcoes0 ul{
    flex-direction: column;
}

.product-wrapper .product-form .cor_variacao ul li{
    margin: 0;
}

.product-wrapper .product-form .cor_variacao ul li{
    position: relative;
    margin: .25rem;
}

.product-wrapper .product-form .cor_variacao li img{
    display: block;
    border: 2px solid var(--color_gray_dark);
    box-shadow: 0 0 0 var(--color_primary_medium);
    transition: ease-out 0.2s;
}

.product-wrapper .product-form .cor_variacao li img:hover,
.product-wrapper .product-form .cor_variacao li img.cor_selecionada{
    box-shadow: 0 0 0 2px var(--color_secondary_medium);
    border-color: #fff;
}

.product-wrapper .product-form .cor_variacao select{
    width: 100%;
    display: block;
    padding-left: 18px;
    font-size: .75rem;
    max-width: 350px;
}

.product-wrapper .product-form .cor_variacao.passo2{
    margin-top: 10px;
}

.product-wrapper .product-form .cor_variacao li.sem_estoque::after {
    content: 'x';
    color: #ff0000;
    display: block;
    position: absolute;
    right: -3px;
    bottom: -6px;
    font-size: 18px;
}

.product-wrapper .product-form .cor_variacao li.sem_estoque img.cor_selecionada{
    box-shadow: 0 0 0 2px rgba(0, 0, 0, .5);
}

.product-wrapper .product-form .cor_variacao li > div{
    min-width: 42px;
    height: 42px;
    padding: 0 0.625rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 40px;
    color: var(--color_secondary_medium);
    border: 1px solid var(--color_gray_dark);
    transition: ease-in-out .3s;
}

.product-wrapper .product-form .cor_variacao li.sem_estoque > div,
.product-wrapper .product-form .cor_variacao li.sem_estoque > div.cor_selecionada{
    background: #e4e4e4!important;
    border-color: #e4e4e4;
    color: #707070;
}

.product-wrapper .product-form .cor_variacao li > div.cor_selecionada{
    background-color: var(--color_secondary_medium);
    border-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla){
    display: flex;
    flex-wrap: wrap;
    flex-direction: row !important;
    margin: 0 -5px;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) li{
    padding: 0 5px 5px;
    margin: 0;
    width: 100%;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label{
    position: relative;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label:not(.lista-radios-input) input{
    position: absolute;
    z-index: -1;
    opacity: .001;
    width: 0;
    height: 0;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label:not(.lista-radios-input) span{
    display: block;
    min-width: 42px;
    min-height: 42px;
    line-height: 20px;
    text-align: center;
    font-size: .875rem;
    font-weight: 600;
    border: 1px solid var(--color_gray_dark);
    transition: ease-in-out .3s;
    cursor: pointer;
    padding: 8px;
    border-radius: 3px;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label:not(.lista-radios-input) span:hover{
    background-color: #efefef;
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label:not(.lista-radios-input) input:checked + span{
    background-color: var(--color_primary_medium);
    border-color: var(--color_primary_medium);
    color: var(--color_gray_dark);
}

.product-wrapper .product-form .currency{
    text-decoration: none;
}

#form_comprar .lista_radios.listaVarMultipla > li,
#form_comprar .lista_radios.listaVarMultipla .labelMultiVariacao,
#form_comprar .lista_radios.listaVarMultipla .labelQuantVariacao{
    display: flex;
    align-items: center;    
}

#form_comprar .lista_radios.listaVarMultipla > li > span{
    font-size: .87rem;
    font-weight: 600;
    margin: 15px 0 8px;
}

#form_comprar .lista_radios.listaVarMultipla > li{
    flex-wrap: wrap; 
    margin-bottom: 15px;   
}

#form_comprar .labelMultiVariacao {
    font-size: .75rem;
    font-weight: 500;
    display: inline-block;
    vertical-align: top;
    width: 63%;
    float: none
}

.product-wrapper .product-form .labelMultiVariacao .multivar{
    margin-right: 5px;
    cursor: pointer;
}

#form_comprar .labelQuantVariacao {
    display: inline-block;
    vertical-align: top;
    text-align: center;
    font-size: .75rem;
    font-weight: 500;
    margin-left: 5px;
    float: none;
    margin-right: 0
}

#form_comprar .labelQuantVariacao .inputQuantVariacao {
    min-width: auto !important;
    margin: 0 !important;
    width: 35px;
    padding: 0 !important;
    text-align: center;
    height: 35px;
}

.product-wrapper .product-form .listaVarMultipla img{
    border: 1px solid var(--color_gray_dark);
    border-radius: 3px;
    margin-right: 5px;    
}

#form_comprar .lista_radios.listaVarMultipla .currency {
    margin-left: 5px;
    text-decoration: none;
}

@media (max-width: 460px){
    .product-wrapper .product-form .listaVarMultipla img {
        max-width: 38px;
    }
}

.product-wrapper .product-form [style="clear:both;"]{
    display: none;
}

.product-wrapper .product-form .product-gifts{
    margin: 1.25rem 0;
    text-align: center;
}

.product-wrapper .product-form .product-price-tray br,
.product-wrapper .product-form .product-price-tray .txt-por,
.product-wrapper .product-form .product-price-tray .produto-economize,
.product-wrapper .product-form .product-price-tray #info{
    display: none;
}

.product-wrapper .product-form .product-price-tray #info_preco br:not(:first-of-type){
    display: block;
}

.product-wrapper .product-form .product-price-tray #produto_preco span.color-tone-2:first-child{
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: lowercase;
}

.product-wrapper .product-form .product-price-tray #produto_preco .currency{
    text-decoration: none;
    text-transform: uppercase;
}

.product-wrapper .product-form .product-price-tray #produto_preco{
    display: flex;
    flex-direction: column;
    align-items: center;
}

.product-wrapper .product-form .product-price-tray #produto_preco #precoDe{
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: line-through;
    text-transform: lowercase;
    color: var(--color_font_light);
}

.product-wrapper .product-form .product-price-tray #produto_preco .PrecoPrincipal{
    font-size:  1.5rem;
    font-weight: 600;
    color: var(--color_primary_medium)
}

.product-wrapper .product-form .product-price-tray #produto_preco #info_preco{
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color_font_light);
    text-align: right;
}

.product-wrapper .product-form .product-price-tray #produto_preco #info_preco strong{
    font-weight: 500;
}

.product-wrapper .product-form .product-batch-promotion{
    margin: 1.25rem 0;
}

.product-wrapper .product-form .product-batch-promotion img{
    display: block;
    width: 100%;
    max-width: 100px;
}

.product-wrapper .product-form .product-batch-promotion.text-promotion {
    padding: 8px;
    border: solid 1px var(--color_gray_dark);
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.product-wrapper .product-form .product-progressive-discount{
    background-color: var(--color_gray_medium);
    margin: 1.25rem 0;
    padding: 0.9375rem 1.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 2px;
    text-align: center;
}

.product-wrapper .product-form .product-progressive-discount .title{
    display: block;
    margin-bottom: 0.9375rem;
    font-size: 0.875rem;
    text-align: center;
    color: var(--color_font_dark);
}

.product-wrapper .product-form .product-progressive-discount .discounts .discount{
    padding: 0.1875rem 0;
}

.product-wrapper .product-form .product-progressive-discount .tooltip{
    display: inline-block;
    position: relative;
    vertical-align: middle;
    margin-left: 5px;
}

.product-wrapper .product-form .product-progressive-discount .tooltip .icon{
    color: var(--color_primary_medium);
    cursor: pointer;
}

.product-wrapper .product-form .product-progressive-discount .tooltip-content{
    position: absolute;
    width: 200px;
    left: 50%;
    padding: 0.625rem;
    font-size: 12px;
    font-weight: 400;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0, 0.11);
    transform: translateX(-50%);
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.2s;
}

.product-wrapper .product-form .product-progressive-discount .tooltip-content::before{
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    width: 8px;
    height: 4px;
    background-color: #fff;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    transform: translateX(-50%);
}

.product-wrapper .product-form .product-progressive-discount .tooltip:hover .tooltip-content{
    opacity: 1;
    visibility: visible;
}

.product-wrapper .product-form .product-reward{
    margin: 1.25rem 0;
    padding: 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    border: solid 1px var(--color_gray_medium);
}

.product-wrapper .product-form .product-reward strong:first-child{
    font-weight: 700;
}

.product-wrapper .product-form .product-reward strong:last-child{
    font-weight: 500;
    margin-left: 2px;
}


/* Product Kit */

.product-wrapper .product-form .product-kit{
    margin: 1.875rem 0;
}

.product-wrapper .product-form .tray-kit{
    font-size: .875rem;
    border: 1px solid var(--color_gray_dark);
    border-radius: 2px;
}

.product-wrapper .product-form .tray-kit .tray-kit-title h3{
    margin: 0.625rem 0;
    font-size: .875rem;
    font-weight: 600;
    color: var(--color_font_dark);
    text-align: center;
}

.product-wrapper .product-form .tray-kit .tray-kit-item{
    padding: 0.625rem;
    border-top: 1px solid var(--color_gray_dark);
}

.product-wrapper .product-form .tray-kit .tray-kit-item > div{
    display: flex;
    position: relative;
}

.product-wrapper .product-form .tray-kit .tray-kit-image{
    width: 70px;
    padding-right: 20px;
}

.product-wrapper .product-form .tray-kit .tray-kit-image img{
    max-width: 100%;
    display: block;
    margin: auto;
}

.product-wrapper .product-form .tray-kit .tray-kit-unity{
    font-size: 0.75rem;
    width: 60px;
    text-align: center;
}

.product-wrapper .product-form .tray-kit .tray-kit-info{
    width: calc(100% - 150px);
}

.product-wrapper .product-form .tray-kit .tray-kit-info h3{
    font-size: .875rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: var(--color_font_dark);
}

.product-wrapper .product-form .tray-kit .texto_variacao h2{
    color: var(--color_font_light);
}

.product-wrapper .product-form .tray-kit .cor_variacao li{
    margin: 0.3125rem;
}

.product-wrapper .product-form .tray-kit .cor_variacao li > div{
    font-size: 0.75rem;
}

/* Product add to cart */

.product-wrapper .product-form .actions{
    margin-top: 1.25rem;
}

.product-wrapper #product-form-box{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.product-wrapper .product-form #quantidade label{
    font-size: 0;
}

.product-wrapper .product-form #quant{
    height: 56px;
    width: 80px;
    text-align: center;
    background: var(--color_gray_dark);
}

.product-wrapper .product-form #quantidade{
    margin-right: 10px;
}

.product-wrapper .product-form #quantidade,
.product-wrapper .product-form #quant{
    font-size: .87rem;
    font-weight: 600;
    color: var(--color_font_light);
}

.product-wrapper .product-form #quant:focus{
    box-shadow: inset 0 0 8px var(--color_font_light);
}

@media (max-width: 500px){
    .product-wrapper .product-form #quant{
        font-size: 16px;
    }
}

.product-wrapper .product-form #bt_comprar{
    max-width: 65%;
    width: 100%;
}

.product-wrapper .product-form #button-buy{
    height: 56px;
    width: 100%;
    padding: 0;
}

.product-wrapper .product-form #button-buy img {
    max-width: 100%;
}

.product-wrapper .product-form #button-buy span{
    font-size: 0;
}

.product-wrapper .product-form #button-buy span::before{
    content: 'COMPRAR PRODUTO';
    font: 700 .87rem var(--font_family);
}

.product-wrapper .product-form .actions .message{
    display: block;
    margin-top: 1.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
}

/* Product variant not available */

.product-wrapper .product-form .product-price-tray #produto_nao_disp h3{
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    display: block;
    line-height: 1.25;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response label{
    width: calc(100% - 120px);
    max-width: unset;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response #email_avise{
    border-radius: 2px 0 0 2px;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letMeKnow{
    height: 52px;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letMeKnow::before{
    display: block;
    box-sizing: border-box;
    width: 120px;
    padding: 0;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 0 2px 2px 0;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #nao_disp .blocoAlerta{
    width: 100%;
}

#produto_nao_disp #letmeknow_response h5{
    display: none;
}

#produto_nao_disp #letmeknow_response {
    width: 100%;
    display: flex;
    align-items: end;
}

/* Product not available - Under request */

.product-wrapper .product-form .product-not-sale .produto-formas-pagamento{
    display: none;
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta{
    font-size: 0;
    line-height: 0;
    text-align: center;
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta::before{
    content: 'Produto sob consulta';
    display: block;
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    color: var(--color_font_dark);
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta::after{
    content: 'Preencha o nosso formul\00E1 rio de produtos sob consulta.';
    display: block;
    margin-top: -0.9375rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.4;
}

.product-wrapper .product-form .product-not-sale .botao_tire_duvidas{
    margin-top: 25px;
    font-size: 0;
    line-height: 0;
}

.product-wrapper .product-form .product-not-sale .botao_tire_duvidas::before{
    content: "Preencher formul\00E1 rio";
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 1.875rem;
    text-align: center;
    line-height: 48px;
    text-transform: uppercase;
    border-radius: 2px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    transition: ease-out 0.2s;
}

.product-wrapper .product-form .product-not-sale .botao_tire_duvidas:hover::before{
    opacity: .8;
}

.product-wrapper .product-form .product-not-sale .botao-nao_indisponivel::before {
    content: "Produto indispon\00ED vel :(";
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    display: block;
    line-height: 1.25;
}

.product-wrapper .product-form .product-not-sale #nao_disp::before,
.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response::before {
    content: "Preencha seu e-mail que te avisaremos quando o produto estiver dispon\00ED vel para compra!";
    display: block;
    font-size: .875rem;
    margin-top: -0.9375rem;
    font-weight: 400;
    text-align: center;
}

.product-wrapper .product-form .product-not-sale #nao_disp::before {
    margin-top: -0.9375rem;
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response::before {
    margin-top: 0.9375rem;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp label{
    width: calc(100% - 120px);
    max-width: unset;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp #email_avise{
    border-radius: 2px 0 0 2px;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #letMeKnow{
    height: 52px;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #letMeKnow::before{
    display: block;
    box-sizing: border-box;
    width: 120px;
    padding: 0;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 0 2px 2px 0;
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp .blocoAlerta{
    width: 100%;
}

/* Product Shipping */

.product-wrapper .product-form .product-shipping{
    margin-top: 1.5rem;
}

.product-wrapper .product-form .product-shipping .produto-calcular-frete{
    display: none;
}

.product-wrapper .product-form .product-shipping .info{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.625rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color_font_dark);
}

.product-wrapper .product-form .product-shipping .info .icon{
    font-size: 1.75rem;
    margin-right: 0.625rem;
}

.product-wrapper .product-form .product-shipping .shipping-form{
    position: relative;
    max-width: 300px;
    margin: 0 auto;
}

.product-wrapper .product-form .product-shipping .input{
    width: 100%;
    height: 42px;
    padding: 0 6.25rem 0 1.25rem;
    color: var(--color_font_medium);
    border: solid 2px var(--color_gray_dark);
    border-radius: 4px;
    background-color: transparent;
}

.product-wrapper .product-form .product-shipping .input:focus{
    box-shadow: inset 0 0 8px var(--color_gray_dark);
}

.product-wrapper .product-form .product-shipping .submit-shipping{
    position: absolute;
    top: 0;
    right: 0;
    height: 42px;
    padding: 0 1.875rem 0 1.25rem;
    font-family: var(--font_family);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    background-color: transparent;
    color: var(--color_primary_medium);
    transition: ease-out 0.2s;
}

.product-wrapper .product-form .product-shipping .submit-shipping:hover{
    opacity: .8;
}

.product-wrapper .product-form .product-shipping .result.loading{
    position: relative;
    min-height: 90px;
    margin-top: 1.875rem;
}

.product-wrapper .product-form .product-shipping .result.loaded{
    margin-top: 1.875rem;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table{
    font-size: 0.875rem;
    width: 100%;
    border: 0 none;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table th,
.product-wrapper .product-form .product-shipping .shipping-rates-table td{
    padding: 0.45rem;
    border: 0 none;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table td{
    font-size: .75rem;
    text-align: center;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table th{
    font-weight: 600;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table td strong{
    font-weight: 400;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table th:first-child,
.product-wrapper .product-form .product-shipping .shipping-rates-table td:first-child{
    width: 130px;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table th:nth-child(2),
.product-wrapper .product-form .product-shipping .shipping-rates-table td:nth-child(2){
    width: 100px;
}

.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n) td,
.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n) th{
    background-color: rgba(0, 0, 0, 0.02);
}

.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n+1) td,
.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n+1) th{
    background-color: rgba(0, 0, 0, 0.05);
}

.product-wrapper .product-form .product-shipping .shipping-rates-table tr td img{
    max-width: 70px;
}

@media (max-width: 1080px){

    .product-wrapper .product-form .product-shipping .shipping-rates-table tr td img{
        max-width: 40px;
    }

    .product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n) td:last-child{
        min-width: 100px;
    }

}

.product-wrapper .product-form .product-social-share{
    margin-top: 1.875rem;
    text-align: center;
}

.product-wrapper .product-form .product-social-share .addthis_counter.addthis_pill_style.at_native_button{
    margin-left: 0.625rem;
}

.addthis_inline_share_toolbox{
    text-align: center;
    margin-top: 15px;
}

@media (min-width: 981px) and (max-width: 1200px){

    .product-wrapper .product-box .product-gallery,
    .product-wrapper .product-box .product-form{
        width: 50%;
    }
}

@media (max-width: 980px){

    .product-wrapper .product-box{
        flex-direction: column;
    }

    .product-wrapper .product-box .product-gallery,
    .product-wrapper .product-box .product-form{
        width: 80%;
        margin: 0 auto;
    }

    .product-wrapper .product-box .product-gallery{
        margin-bottom: 40px;
    }

    .products-history #produtos ul {
        width: 33%;
    }

}

@media (max-width: 767px){

    .product-wrapper{
        margin-bottom: 2.5rem;
    }    

    .product-wrapper .product-box .product-gallery{
        width: 100%;
        padding: 0;
        max-width: 500px;
        margin: 0 auto;
    }

    .product-wrapper .product-gallery .product-images img.zoomImg{
        display: none;
    }

    .product-wrapper .product-box .product-video .text{
        width: 70px;
    }

    .product-wrapper .product-box .product-form{
        width: 100%;
        max-width: 500px;
        margin: 2.5rem auto 0;
        padding: 2.5rem 1.25rem;
    }

    .product-wrapper .product-form .product-tags .tag{
        width: 40%;
        max-width: 160px;
    }

    .product-wrapper .product-gallery .product-thumbs .thumbs-list{
        margin: 0;
    }

    .products-history #produtos ul {
        width: 100%;
    }

}

@media (max-width: 575px){

    .product-wrapper .product-form .product-progressive-discount .tooltip{
        display: none;
    }

}

@media (max-width: 460px){

    .product-wrapper .product-form #bt_comprar {
        max-width: 57%;
    }

}


/* Compre junto */

.section-buy-together{
    position: relative;
}

.section-buy-together .buy-together-loader{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
}

.section-buy-together .buy-together-loader.show{
    opacity: 1;
    visibility: visible;
}

.compreJunto form{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.compreJunto form .fotosCompreJunto{
    width: calc(100% - 245px);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-right: 40px;
}

#ProdBlockCompreJunto #ProdAbas{
    display: none;
}

.compreJunto > li:nth-child(n+2){
    margin-top: 40px;
}

.compreJunto .produto{
    width: 33.3333%;
    padding: 0 10px 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.compreJunto .unidades_topo{
    display: none;
}

.compreJunto .produto > span{
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
}

.compreJunto .produto span > div,
.compreJunto .produto span > a{
    display: block;
    position: relative;
    margin-bottom: 10px;
}

.compreJunto .produto span > div::before,
.compreJunto .produto  span > a::before{
    content: '';
    display: block;
    padding-bottom: 100%;
}

.compreJunto .produto .product-name{
    color: var(--color_font_dark);
    font-weight: 600;
}

.compreJunto .produto img{
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    max-width: 90%;
    max-height: 90%;
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.2s;
}

.compreJunto .produto img.loaded{
    opacity: 1;
    visibility: visible;
}

.compreJunto .precoCompreJunto{
    width: 280px;
    padding: 25px;
    background-color: var(--color_gray_medium);
}

.fotosCompreJunto .plus{
    width: 20px;
    position: relative;
    z-index: 2;
    display: flex;
    font-size: 0;
    align-items: center;
}

.fotosCompreJunto .plus::before{
    content: '\ea10';
    font-family: "go" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    display: block;
    width: 20px;
    height: 44px;
    border-radius: 3px;
    text-align: center;
    font-size: 1.25rem;
    line-height: 42px;
    color: var(--color_font_dark);
}

.fotosCompreJunto .plus.to::before{
    content: '\ea08';
    font-family: "go" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
}

.compreJunto .varCont{
    margin-bottom: 10px;
}

.compreJunto .varCont input[type='text'],
.compreJunto .varCont select{
    width: 100%;
    display: block;
    height: 42px;
    padding-left: 14px;
    max-width: 280px;
}

.compreJunto .varTit{
    margin: 0.9375rem 0 0.3125rem 0;
    font-weight: 600;
    font-size: .75rem;
}

.compreJunto .varCont + .varTit:not(.onVar){
    display: none;
}

.comprejuto_preco{
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: line-through;
}

.comprejuto_preco span{
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: line-through;
}

.comprejunto_preco2{
    font-size: .938rem;
    font-weight: 700;
}

.comprejunto_preco2 strong:first-child{
    font-size: 0;
}

.comprejunto_preco2 .PrecoPrincipal{
    color: var(--color_primary_medium);
    font-size: 1.5rem;
    font-weight: 600;
    margin-left: -0.3125rem;
}

.comprejunto_economize{
    font-size: .75rem;
    font-weight: 500;
    margin: 5px 0 0 0;
}

.botao-commerce.botao-compre-junto:not(.botao-sob-consulta){
    width: 100%;
    margin: 1.875rem 0 0 0;
    padding: 0;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    border-radius: 2px;
    transition: ease-out 0.2s;
}

.botao-compre-junto:not(.botao-sob-consulta):hover{
    opacity: .8;
}

.botao-compre-junto:not(.botao-sob-consulta) .botao-commerce-img{
    font-size: 0;
}

.botao-compre-junto:not(.botao-sob-consulta) .botao-commerce-img::before{
    content: 'Comprar Junto';
    font-family: var(--font_family);
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 50px;
    text-transform: uppercase;
}

.precoCompreJunto > div:first-child{
    font-size: 0;
}

.precoCompreJunto > div:first-child strong{
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.875rem;
    display: block;
    color: var(--color_font_dark);
}

.compre-junto{
    margin-top: 60px;
}

@media (max-width: 767px) {

    .compreJunto form{
        flex-direction: column;
    }

    .compreJunto form .fotosCompreJunto{
        width: 100%;
        padding: 0 0 20px;
        flex-direction: column;
        align-items: center;
    }

    .compreJunto .produto{
        width: 100%;
        max-width: 300px;
    }

    .compreJunto .fotosCompreJunto .plus{
        width: 100%;
        transform: initial;
        text-align: center;
        line-height: .8;
        justify-content: center;
    }

    .compreJunto .precoCompreJunto{
        width: 100%;
    }

}


/* Product tabs */

.product-wrapper .product-tabs{
    margin: 5rem 0 0 0;
}

.product-tabs .tabs-content .payment-tab{
    position: relative;
    min-height: 75px;
}

.product-tabs .tabs-content .payment-tab .item-parcela{
    display: block;
}

.product-tabs .tabs-content .payment-tab .option a{
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.9375rem 0;
    font-size: 0.875rem;
    font-weight: 500;
    border-bottom: solid 1px var(--color_gray_dark);
}

.product-tabs .tabs-content .payment-tab .option a::after{
    content: "\ea01";
    position: absolute;
    top: calc(50% - 4px);
    right: 0;
    margin-left: 0.3125rem;
    font-family: "go" !important;
    font-size: 0.75rem;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    transition: ease-out 0.2s;
}

.product-tabs .tabs-content .payment-tab .option.show a::after{
    transform: rotate(-90deg);
}

.product-tabs .tabs-content .payment-tab .option a img{
    width: 45px;
    margin-right: 0.625rem;
}

.product-tabs .tabs-content .payment-tab .option-details {
    padding: 0.9375rem 0 2.5rem 0;
}

.product-tabs .tabs-content .payment-tab .option.show .option-details{
    border-bottom: solid 1px var(--color_gray_dark);
    transition: border 0.1s ease-out 0.3s;
}

.product-tabs .tabs-content .payment-tab .option-details table{
    width: 100%;
    font-size: 0.875rem;
    line-height: 1.6;
}

.product-tabs .tabs-content .payment-tab .option-details table strong,
.product-tabs .tabs-content .payment-tab .option-details table b{
    font-weight: 600;
}

@media (min-width: 1071px){

    .product-wrapper .product-tabs .tabs-nav .tab-link{
        padding: 0.625rem 1.25rem 1.25rem 1.25rem;
        font-size: 1.125rem;
    }
}

@media (min-width: 768px) and (max-width: 1070px){

    .product-wrapper .product-tabs .tabs-nav .tab-link{
        padding: 0.625rem 0.625rem 1.25rem 0.625rem;
        font-size: 1rem;
    }

}

@media (min-width: 768px) and (max-width: 860px){

    .product-wrapper .product-tabs .tabs-nav .tab-link .text{
        text-align: center;
    }

}

@media (min-width: 768px){

    .product-wrapper .product-tabs .tabs-nav{
        display: flex;
        margin-bottom: 30px;
        border-bottom: solid 1px var(--color_gray_dark);
    }

    .product-wrapper .product-tabs .tabs-nav .tab-link{
        display: block;
        font-weight: 500;
        color: var(--color_font_light);
        text-decoration: none;
        transition: ease-out 0.2s;
    }

    .product-wrapper .product-tabs .tabs-nav .tab-link .text{
        position: relative;
        display: block;
    }

    .product-wrapper .product-tabs .tabs-nav .tab-link .text::after{
        content: '';
        position: absolute;
        left: 0;
        bottom: -21px;
        width: 0;
        height: 3px;
        background-color: var(--color_primary_medium);
        transition: ease-out 0.2s;
    }

    .product-wrapper .product-tabs .tabs-nav .tab-link.active,
    .product-wrapper .product-tabs .tabs-nav .tab-link:not(.active):hover{
        color: var(--color_primary_medium);
    }

    .product-wrapper .product-tabs .tabs-nav .tab-link.active .text::after,
    .product-wrapper .product-tabs .tabs-nav .tab-link:not(.active):hover .text::after{
        width: 100%;
    }

    .product-tabs .tabs-content .tab-link-mobile{
        display: none;
    }

    .product-tabs .tabs-content .tab{
        display: none;
    }

    .product-tabs .tabs-content .tab.active{
        display: block;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:first-child,
    .product-tabs .tabs-content .payment-tab .option-details table td:first-child{
        width: 100px;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:nth-child(2),
    .product-tabs .tabs-content .payment-tab .option-details table td:nth-child(2){
        width: 400px;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:last-child,
    .product-tabs .tabs-content .payment-tab .option-details table td:last-child{
        vertical-align: top;
    }

}

@media (max-width: 767px){

    .product-wrapper .product-tabs{
        margin-top: 2.5rem;
        margin-left: -10px;
        margin-right: -10px;
    }

    .product-tabs .tabs-nav{
        display: none;
    }

    .product-tabs .tabs-content .tab-link-mobile{
        display: block;
        position: relative;
        width: 100%;
        padding: 0.9375rem 2.875rem 0.9375rem 0.9375rem;
        color: var(--color_font_dark);
        font-size: 1.125rem;
        font-weight: 500;
        text-align: center;
        text-decoration: none;
        border-bottom: 1px solid var(--color_gray_dark);
        transition: ease-out 0.2s;
    }

    .product-tabs .tabs-content .tab-link-mobile:first-child{
        border-top: 1px solid var(--color_gray_dark);
    }

    .product-tabs .tabs-content .tab-link-mobile::after{
        content: '\ea01';
        position: absolute;
        top: 19px;
        right: 15px;
        margin-left: 0.3125rem;
        font-family: "go" !important;
        font-size: 0.75rem;
        font-style: normal !important;
        font-weight: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        transition: ease-out 0.2s;
    }

    .product-tabs .tabs-content .tab-link-mobile.active{
        color: var(--color_primary_medium);
    }

    .product-tabs .tabs-content .tab-link-mobile.active::after{
        transform: rotate(-90deg);
    }

    .product-tabs .tabs-content .tab{
        display: none;
        padding: 1.875rem 0.625rem;
    }

    .product-tabs .tabs-content .tab.active{
        border-bottom: solid 1px var(--color_gray_dark);
        transition: border 0.1s ease-out 0.3s;
    }

    .product-tabs .tabs-content .tab.payment-tab{
        padding-top: 0.625rem;
        padding-bottom: 1.25rem;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:first-child,
    .product-tabs .tabs-content .payment-tab .option-details table td:first-child{
        width: 20%;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:nth-child(2),
    .product-tabs .tabs-content .payment-tab .option-details table td:nth-child(2){
        width: 80%;
    }

    .product-tabs .tabs-content .payment-tab .option-details table th:last-child,
    .product-tabs .tabs-content .payment-tab .option-details table td:last-child{
        display: none;
    }

    .product-tabs .tabs-content .payment-tab .option:last-child a{
        border-bottom: none;
    }

    .product-tabs .tabs-content .payment-tab .option:last-child.show a{
        border-bottom: solid 1px var(--color_gray_dark);
    }

    .product-tabs .tabs-content .payment-tab .option:last-child.show .option-details{
        border-bottom: none;
        padding-bottom: 0;
    }

}


/* Product review comments */

#coments .product-comments{
    font-size: .875rem;
    display: flex;
    flex-direction: column;
}

#coments h2{
    margin-bottom: 1.875rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    color: var(--color_font_dark);
}

#coments .botao-commerce{
    margin-top: 20px;
}

#coments br{
    display: none;
}

#coments a{
    text-decoration: underline;
}

#coments a[data-logged-user="false"]{
    text-align: center;
    display: block;
    text-decoration: none;
    transition: ease-out 0.2s;
    margin: 0.3125rem auto 0;
    padding: 0.625rem 1.25rem;
    font-family: var(--font_family);
    font-size: 0.75rem;
    font-weight: 700;
    color: var(--color_font_inverted) !important;
    background-color: var(--color_secondary_medium);
    border-radius: 4px;
    transition: ease-out 0.2s;
    text-transform: uppercase;
    max-width: 250px;
}

#coments a[data-logged-user="false"]:hover{
    opacity: .8;
}

#coments #form-comments{
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

#coments #form-comments label{
    margin-bottom: 0.9375rem;
}

#coments #form-comments label h3{
    margin-top: 0;
}

#coments #form-comments #nome_coment,
#coments #form-comments #email_coment {
    display: block;
    width: 100%;
    margin: 5px 0 0 0;
}

#coments #form-comments h5{
    margin-top: -0.625rem;
}

#coments #form-comments h3{
    font-size: .875rem;
    margin: 1.25rem 0 0 0;
    font-weight: 400;
}

#coments .submit-review{
    width: 160px;
    height: 42px;
    margin: 1.25rem auto 0;
    font-family: var(--font_family);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--color_font_inverted);
    border-radius: 2px;
    background-color: var(--color_secondary_medium);
    transition: ease-out 0.2s;
}

#coments .submit-review:hover{
    opacity: .8;
}

#coments .blocoSucesso{
    margin: 0;
    text-align: center;
}

.rateBlock{
    margin-top: 0.3125rem;
}

.rateBlock .stars,
#coments .ranking .rating{
    display: flex;
    align-items: center;
}

.rateBlock .starn,
#coments .ranking .icon{
    display: block;
    width: .938em;
    height: .938em;
    margin-right: 0.1875rem;
    background: url(../img/empty-star.svg) no-repeat;
    background-size: 100%;
    cursor: pointer;
}

.rateBlock .starn.star-on,
#coments .ranking .icon.active{
    background-image: url(../img/star.svg);
}

.rateBlock .nota{
    margin-left: 0.625rem;
}

.rateBlock .nota strong{
    font-weight: 600;
}

#coments .ranking .rating{
    width: 85px;
}

#coments .board{
    display: flex;
    flex-direction: column;
}

#coments .board > h2:first-child{
    margin-top: 40px;
}

#coments .blocoSucesso ~ *{
    order: -1;
}

.hreview-comentarios{
    margin-bottom: 1.25rem;
    padding: 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
}

.hreview-comentarios h3{
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    color: var(--color_primary_medium);
}

.hreview-comentarios h4{
    font-size: 0.75rem;
    font-weight: 400;
}

.hreview-comentarios h3{
    font-size: 1rem !important;
    font-weight: 700 !important;
}

.hreview-comentarios .ranking{
    display: flex;
    align-items: center;
    margin: 0.625rem 0;
    font-weight: 500;
}

.hreview-comentarios .description{
    margin: 5px 0;
    font-size: 0.875rem;
}


/* Product page related products */

.section-product-related {
    margin-left: -10px;
    margin-right: -10px;
}

.section-product-related .swiper-container{
    padding-bottom: 40px;
    margin-bottom: -40px;
}

@media (max-width: 767px){

    .section-product-related {
        margin-left: 0;
        margin-right: 0;
    }

}


/* Product page history */

.products-history{
    margin-top: 5rem;
}

.products-history .visitados_produtos{
    display: flex;
    align-items: center;
    flex-direction: column;
}

.products-history #produtos{
    display: flex;
    margin-left: -10px;
    flex-wrap: wrap;
}

.products-history #produtos ul{
    padding: 0 10px;
    margin: 10px 0;
}

.products-history #produtos ul li{
    padding-right: 5px;
    width: 300px;
}

.products-history #produtos ul li::after{
    content: '';
    clear: both;
    display: block;
}

.products-history #produtos ul li > a{
    float: left;
    height: 90px;
    width: 90px;
    display: block;
}

.products-history #produtos ul li > :nth-child(n+2){
    margin-left: 100px;
    display: block;
}

.products-history .ValoresLista{
    font-size: 0.75rem;
}

.products-history .ValoresLista .precode{
    text-decoration: line-through;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
}

.products-history .ValoresLista .oculta_destaque{
    display: none;
}

.products-history .ValoresLista .precoAvista{
    font-weight: 700;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--color_primary_medium);
    font-size: 14px;
}

.products-history .ValoresLista strong{
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
}

.products-history #produtos img{
    display: block;
    margin: auto;
    width: 100%;
    height: 100%;
    object-fit: scale-down;
    object-position: center;
}

.products-history .NomeProdLista{
    color: var(--color_font_dark);
    font-size: 0.875rem;
    margin: 10px 0;
    line-height: 1.3;
    display: block;
    font-weight: 500;
}

.products-history .ValoresLista img[src*="sobconsulta"]{
    display: none !important;
}

.products-history .ValoresLista img[src*="sobconsulta"] + .botao-commerce{
    display: inline;
    padding: 0.3125rem 0.625rem;
    font-size: 0.75rem;
}

.products-history .ValoresLista img[src*="sobconsulta"] + .botao-commerce:hover{
    background-color: var(--color_primary_medium);
}

.products-history .products-history-wrapper{
    position: relative;
}

.products-history .products-history-wrapper .history-loader{
    position: absolute;
    min-height: 70px;
    display: none;
}

.products-history .history-loader.show{
    display: flex;
}

.paginacao_ajax{
    order: 2;
    margin-top: 15px;
}

.clearVisiteds, #visitados_itens{
    display: none;
}

.products-history .total_produtos{
    display: none;
}

.products-history .paginacao_ajax_prod{
    display: block;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
}

#linksPag a{
    display: inline-block;
    line-height: 26px;
    width: 26px;
    text-align: center;
    color: var(--color_font_inverted);
    background: var(--color_primary_medium);
    padding: 0 4px;
    margin: 0 4px;
}

#linksPag a:not(.pageON){
    background: transparent;
    color: var(--color_font_light);
}

@media (max-width: 767px){

    .products-history .visitados_produtos{
        flex-direction: column;
        align-items: center;
    }

    .products-history #produtos{
        width: 100%;
        flex-direction: column;
        align-items: center;
    }

    .paginacao_ajax{
        text-align: center;
        margin-top: 30px
    }
}


/* News list page */

.page-busca_noticias hr{
    border: 0;
}

.page-busca_noticias h1{
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--color_font_dark);
}

#listaCategoriasNoticias {
    display: flex;
    margin-bottom: 1.875rem;
    font-size: 0;
}

#listaCategoriasNoticias li a{
    margin-right: 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    transition: ease-out 0.2s;
}

#listaCategoriasNoticias li a:hover{
    color: var(--color_primary_medium);
}

#listaCategoriasNoticias li a b,
#listaCategoriasNoticias li a strong{
    color: var(--color_primary_medium);
    font-weight: 500;
}

.page-busca_noticias .board .left{
    margin: 20px 0 0;
    font-size: .875rem;
}

.page-busca_noticias .page-content,
.page-depoimentos .page-content{
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.4s;
}

.page-depoimentos .page-content.show,
.page-busca_noticias .page-content.show{
    opacity: 1;
    visibility: visible;
}

.page-busca_noticias:not(.show-menu) .nav .second-level,
.page-busca_noticias:not(.show-menu) .menu-mobile,
.page-depoimentos:not(.show-menu) .nav .second-level,
.page-depoimentos:not(.show-menu) .menu-mobile,
.page-depoimentos:not(.show-menu) .modal-store-reviews{
    display: none;
}

.page-busca_noticias .noticias{
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.625rem;
}

.page-busca_noticias .noticias li{
    width: 33.333333%;
    padding: 1.25rem 0.625rem;
}

.page-busca_noticias .box-noticia #noticia_imagem img{
    opacity: 1;
}

@media (max-width: 800px){

    .page-busca_noticias .noticias li{
        width: 50%;
    }

}

@media (max-width: 767px){

    .page-busca_noticias h1{
        font-size: 1.25rem;
    }

}

@media (max-width: 575px){

    .page-busca_noticias .noticias li{
        width: 100%;
    }

}


/* News page */

.page-noticia .dataNoticia{
    display: none;
}

.page-noticia .board .dataNoticia + h2{
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1.875rem 0;
    color: var(--color_font_dark);
    text-decoration: none;
}

.page-noticia .board > img {
    float: left;
}

.page-noticia .board img {
    max-width: 100%;
    margin-bottom: 15px;
    margin-right: 30px;
    border-radius: 8px;
    height: auto !important;
}

.page-noticia .board p{
    font-size: .875rem;
    margin-bottom: 15px;
}

@media (max-width: 767px){

    .page-noticia .board .dataNoticia + h2{
        font-size: 1.375rem;
    }

    .page-noticia .board img{
        display: block;
        margin: 0 auto 15px;
        float: none;
    }

}


/* News list page pagination */

.btn-primeira-pagina,
.separador-paginas,
.btn-ultima-pagina{
    display: none;
}

.page-busca_noticias .board + .container3 .right{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1.875rem;
}

.btns-paginator{
    display: block;
    min-width: 44px;
    height: 38px;
    margin: 0.3125rem;
    font-size: 0.875rem;
    font-weight: 700;
    line-height: 38px;
    text-align: center;
    background-color: var(--color_gray_dark);
    color: var(--color_font_medium);
    border-radius: 2px;
    transition: ease-out 0.2s;
}

.btns-paginator img{
    display: none;
}

.btns-paginator:hover,
.btns-paginator.selectedPg{
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    font-size: 0.875rem;
    font-weight: 700;
    text-align: center;
    line-height: 38px;
}

.btns-paginator.btn-primeira-pagina,
.btns-paginator.btn-pagina-anterior,
.btns-paginator.btn-proxima-pagina,
.btns-paginator.btn-ultima-pagina{
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btns-paginator.btn-pagina-anterior::before,
.btns-paginator.btn-primeira-pagina::before,
.btns-paginator.btn-primeira-pagina::after{
    content: '\ea02';
    font-family: 'go' !important;
    font-size: 0.625rem;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
}

.btns-paginator.btn-proxima-pagina::before,
.btns-paginator.btn-ultima-pagina::before,
.btns-paginator.btn-ultima-pagina::after{
    content: '\ea03';
    font-family: 'go' !important;
    font-size: 0.625rem;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
}

.page-busca_noticias .Seguro ~ hr ~ *{
    display: none;
}

.store-review-paginator{
    display: flex;
    flex-wrap: wrap;
    margin: 1.25rem 0 2.5rem 0;
}

/* Store Reviews page */

.page-depoimentos .page-content #depoimento{
    display: none;
}

.page-depoimentos .page-content{
    min-height: 350px;
}

.page-depoimentos .board h1{
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
}

.page-depoimentos .page-content h2:first-of-type,
.page-depoimentos .page-content h2:last-of-type{
    display: none;
}

.asas::after {
    content: '';
    display: block;
    width: 62px;
    height: 4px;
    margin: 15px auto 0;
    background-color: var(--color_primary_medium);
}

.editDep{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.editDep > li{
    position: relative;
    width: calc(50% - 20px);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5625rem 2.5rem;
    margin-bottom: 1.25rem;
    text-align: center;
    background-color: var(--color_gray_medium);
    color: var(--color_font_medium);
    border-radius: 0.375rem;
    z-index: 0;
}

.editDep strong{
    font-weight: 600;
}

.editDep h3{
    order: 2;
    margin: 0.625rem 0 0 0;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    text-indent: -47px;
    overflow: hidden;
}

.editDep h3 img{
    position: absolute;
    top: 16px;
    left: 20px;
    bottom: 0;
    z-index: -1;
    opacity: 0.7;
    max-width: 80px;
}

.editDep ul li strong,
.editDep ul li:nth-child(2),
.editDep ul li:not(.dep):first-child{
    display: none;
}

.editDep ul li.dep{
    font-size: .875rem;
    line-height: 1.5;
}

#depoimento{
    font-size: .875rem;
}

#depoimento span.error-block{
    font-size: 12px;
    font-weight: 400;
    color: #b00000;
}

#depoimento label{
    display: block;
    margin-bottom: 14px;
}

#depoimento label h3{
    font-size: inherit;
    font-weight: inherit;
}

#depoimento .nota_dep label{
    display: inline-block;
    margin-left: 5px;
}

#depoimento br{
    display: none;
}

.textarea{
    max-width: 100%;
}

#depoimento span.block{
    margin: 5px 0;
    display: block;
}

#depoimento input[type='text'],
#depoimento input[type='email'],
#depoimento textarea,
#depoimento input[size]{
    width: 100%;
    display: block;
    margin-top: 5px;
}

#depoimento #aviso_depoimento{
    margin: 0;
    display: block !important;
}

#depoimento #aviso_depoimento > div{
    margin-top: 1.875rem;
}

.page-depoimentos .board .left{
    font-size: 0;
}

.page-depoimentos .page-content br,
.page-depoimentos .page-content hr{
    display: none;
}

.page-depoimentos .page-content .botao-commerce{
    max-width: 200px;
    width: 100%;
    margin: 30px auto;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    cursor: pointer;
}

.modal-store-reviews h2{
    margin-bottom: 1.875rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    color: var(--color_font_dark);
}

.modal-store-reviews .botao-commerce{
    display: block;
    width: 180px;
    margin: 1.875rem auto 0;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
}

.modal-store-reviews .blocoSucesso{
    text-align: center;
    margin: 0;
}

#enviar_dep{
    display: none;
}

@media (max-width: 767px){

    .store-review-paginator{
        justify-content: center;
    }

    .page-depoimentos .board h1{
        font-size: 1.25rem;
    }

    .editDep > li{
        width: 100%;
        padding: 1.25rem;
    }

    .editDep ul li.dep{
        text-align: justify;
    }

    .editDep h3 img{
        opacity: 1;
    }

}


/* Wishlist */

.wishlist{
    color: var(--color_font_medium);
    padding: 5px;
}

.wishlist a{
    color: inherit;
    transition: ease-out 0.2s;
}

#container-add-lista a{
    font-size: 0;
    display: flex;
}

#container-add-lista a::before {
    content: '\ea0a';
    font-family: 'go' !important;
    font-size: 0.9375rem;
    margin-right: 0.25rem;
    text-transform: none !important;
}

#container-add-lista #bloco-add-lista {
    position: relative;
}

#container-add-lista #bloco-add-lista > a{
    color: var(--color_font_light);
    align-items: center;
}

#container-add-lista #bloco-add-lista > a img{
    margin-left: 3px;
}

#container-add-lista #add-listas {
    display: block !important;
    position: absolute;
    width: 170px;
    top: 1.5625rem;
    padding: 0.625rem;
    color: var(--color_font_dark);
    font-size: 0.75rem;
    font-weight: 400;
    border-radius: 4px;
    background-color: var(--color_gray_dark);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.11);
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.2s;
}

#container-add-lista #add-listas[style*="display: block;"]{
    opacity: 1;
    visibility: visible;
}

#container-add-lista #add-listas::before{
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    width: 8px;
    height: 4px;
    background-color: var(--color_primary_medium);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    transform: translateX(-50%);
}

#container-add-lista .listas + .listas{
    margin-top: 0.625rem;
}

#container-add-lista .listas a {
    font-size: 0.875rem;
    font-weight: 500;
}

#container-add-lista .listas a::before{
    margin-right: 0.4375rem;
    color: var(--color_font_medium);
}

#container-add-lista .listas a:hover,
#container-add-lista .listas a:hover::before{
    color: var(--color_primary_medium);
}

@media(min-width: 1001px){
    #container-add-lista #add-listas{
        left: 50%;
        transform: translateX(-50%);
    }
}

@media(max-width: 1000px){
    #container-add-lista #add-listas{
        right: -1.25rem;
    }
}

/* Wishlists pages */

.page-listas_index .board h2{
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
}

#tipos-listas li{
    margin-bottom: 1.875rem;
}

#tipos-listas h3{
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.625rem;
    text-align: center;
}

#tipos-listas li a{
    display: block;
}


.page-listas_evento h2,
.page-listas_busca h2{
    font-size: 18px;
    font-weight: 500;
    color: var(--color_font_dark);
}

.page-listas_evento table.busca{
    margin-top: 0.9375rem;
}

.page-listas_busca .titCatalogo{
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--color_font_dark);
}

.page-listas_busca form[action*="listas_busca.php"]{
    margin-top: 0.625rem;
}

.page-listas_busca table.busca{
    margin-bottom: 1.875rem;
}

.page-listas_busca h2 ~ *,
.page-listas_busca h2 ~ * strong{
    font-size: 0.875rem;
    font-weight: 400;
}

.page-listas_busca h2 ~ ul{
    margin: 1.875rem 0;
}

.page-listas_busca h2 ~ ul br{
    display: none;
}

.page-listas_busca h2 ~ ul li{
    position: relative;
    padding: 1.25rem;
    border: solid 1px var(--color_gray_dark);
}

.page-listas_busca h2 ~ ul li .NomeProduto{
    font-size: 1rem;
    font-weight: 600;
    color: var(--color_font_dark);
}

.page-listas_busca h2 ~ ul li .NomeProduto + label b{
    font-weight: 400;
}

.page-listas_busca h2 ~ ul li a[href*="lista.php"]{
    position: absolute;
    right: 20px;
    top: calc(50% - 13px);
}


#lista_criar h2{
    margin-top: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
}

#lista_criar input[size="40"],
#lista_criar input[size="50"]{
    width: 100%;
    max-width: 300px;
}

.page-listas_criar .board{
    font-size: .875rem;
}

.page-listas_criar .board td{
    padding: 10px;
    word-break: break-all;
}

.page-print_lista .page-content  h1{
    font-size: 1.375rem;
    text-transform: uppercase;
    font-weight: 600;
    margin: 10px 0;
}

.page-print_lista .page-content h2{
    font-size: 1rem;
}

.lista-produtos{
    text-align: center;
    display: inline-block;
    max-width: 340px;
    margin: 15px;
    width: 100%;
}

.lista-produtos .lista-produto-comprar{
    position: relative;
    width: 100%;
    max-width: 200px;
    height: 42px;
    margin: 0.625rem auto;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    border-radius: 3px;
    transition: ease-out 0.2s;
}

.lista-produtos .lista-produto-comprar::before{
    content: 'Comprar';
    line-height: 40px;
    font-size: 0.875rem;
    text-transform: uppercase;
    font-weight: 600;
}

.lista-produtos .lista-produto-comprar:hover{
    opacity: .8;
}

.lista-produtos .lista-produto-comprar input[type="image"]{
    width: 100%;
    height: 42px;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
}

.lista-imagem img{
    max-width: 100%;
}

.page-lista .board{
    margin: 20px 0;
}

.page-lista .board hr{
    margin: 20px 0;
}

.lista-produtos .qntd{
    border: 1px solid #e5e5e5;
    width: 80px;
    display: block;
    margin: 5px auto;
    height: 35px;
    text-align: center;
}

.page-navegacao_visitados hr{
    margin: 20px 0;
}

.page-navegacao_visitados .change{
    display: none;
}

.page-navegacao_visitados h2{
    font-size: 1.125rem;
    margin: 20px 0;
}

.bts,
.bts2{
    padding: 4px 8px;
    height: 23px;
    text-align: center;
    line-height: 14px;
    font-size: .75rem;
    display: inline-block;
    border-radius: 20px;
    margin: 0 0.3125rem;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    transition: ease-out 0.2s;
}

.bts:hover,
.bts2:hover{
    opacity: .8;
}

.vitrineVisitados{
    margin: 0 -10px;
    display: flex;
    font-size: .875rem;
}

.vitrineVisitados > li{
    padding: 10px;
    width: calc(100% / 3);
}

.nomeProd{
    margin-bottom: 10px;
    display: block;
}

.ranking.hreview-aggregate{
    margin: 5px 0;
}

.editComment li> a{
    display: block;
    width: 90px;
    height: 90px;
}

.editComment li> a img{
    display: block;
    margin: auto;
}

.editComment li{
    margin: 20px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
}

.editComment h3{
    font-size: .75rem;
    font-weight: 600;
    white-space: nowrap;
    position: absolute;
    width: calc(100% - 100px);
    left: 100px;
    top: 0;
}

.editComment .bts{
    margin: 0 0 5px;
}

.editComment .ranking{
    font-weight: 600;
}

.editComment .relativity{
    width: calc(100%);
    padding-top: 25px;
}

.editComment .change{
    font-size: .75rem;
    margin-top: 5px;
}

.consulta-product{
    font-size: .75rem;
    font-weight: 600;
    margin-bottom: 10px;
}

@media (max-width: 1024px){

    .box-gallery .image-show img:nth-child(2){
        display: none !important;
    }

}

@media (max-width: 767px){

    #lista_criar td{
        display: block;
        padding: 5px 0;
    }

    .vitrineVisitados > li{
        width: 100%;
    }

}

#preco_atual[value="0.00"] + #preco{
    margin-top: 30px;
}

#preco_atual[value="0.00"] + #preco br:first-of-type{
    display: none;
}


.filters-list{
    font-size: .75rem;
}

.applied-filters{
    margin: 10px 0;
}

.applied-filters a{
    color: var(--color_primary_medium);
    font-weight: 600;
}

.applied-filters a svg{
    width: 8px;
    height: 8px;
    fill: inherit;
    margin-left: 10px;
}

.sidebar-category li:not(.sub) a.sub-filter::after{
    display: none;
}

.filters-list a{
    display: inline-block;
    padding: 5px 0;
    line-height: 1;
    -webkit-transition: color ease-in-out .2s;
    -o-transition: color ease-in-out .2s;
    transition: color ease-in-out .2s;
}

.filters-list a:hover{
    color: var(--color_primary_medium);
}

.filters-list label{
    cursor: pointer;
    padding: 5px 0;
}

.space-radio{
    padding: 3px 0;
}

.filters-list input[type="checkbox"]{
    position: absolute;
    left: 0;
    z-index: -1;
    opacity: 0.001;
    width: 0;
    height: 0;
}

.filter-button{
    margin: 10px 0;
    width: 100%;
}

.icon-radio{
    width: 18px;
    height: 18px;
    border: 1px solid #707070;
    opacity: .5;
    position: relative;
    margin-right: 14px;
    display: block;
}

.icon-radio::before{
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 10px;
    height: 10px;
    background-color: #707070;
    opacity: 0;
    -webkit-transition: ease-in-out .3s;
    -o-transition: ease-in-out .3s;
    transition: ease-in-out .3s;
}

.filters-list input:checked ~ .icon-radio::before,
.filters-list input:checked ~ .icon-radio::before{
    opacity: 1;
}

/* fim filter */

.col-content .breadcrumb{
    margin: 0 0 22px;
}


@media (max-width: 991px){

    .hide-menu{
        display: none;
    }

}


/* Advanced search */

:not(.container2) > .board > h1{
    font-size: 2rem;
    text-transform: uppercase;
    color: var(--color_font_dark);
}

.page-search #Vitrine {
    font-size: 14px;
}

#vitrine-catalogo h3{
    font-size: 1rem;
}

#vitrine-catalogo .blocoAlerta{
    text-align: center;
}

#vitrine-catalogo{
    padding: 30px 0 0;
}

.page-search h1.color{
    margin-top: 30px;
    text-align: center;
    color: var(--color_font_dark);
}

.page-search #Vitrine fieldset{
    border: 0;
    padding: 0;
    margin: 14px 0;
}

.page-search #Vitrine fieldset p{
    margin: 30px 0;
}

.page-search #Vitrine fieldset input[name="palavra_busca"]{
    display: block;
    margin: 5px 0;
}

.page-search #Vitrine form input[type="checkbox"]{
    margin: 7px 5px 7px 0;
}

.page-search #Vitrine #preco_ini{
    margin-right: 15px;
}

.page-search #Vitrine form{
    max-width: 600px;
    margin: 0 auto;
}


/* Login Modal Tray */

.tray-container__identify,
.tray-container{
    left: calc(50% - 160px);
    width: 100% !important;
    max-width: 320px !important;
    padding: 2.5rem !important;
    border-radius: 4px;
}

tray-login  .tray-title{
    font-family: var(--font_family);
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--color_font_dark);
    text-align: center;
}


/* login */

.Seguro{
    display: none;
}

.page-login .titulo-login{
    display: none;
}

.page-login .carrinho-heading{
    display: none;
}


.page-login .page-content .board{
    display: flex;
}

.caixa-login h3,
.caixa-cadastro h3{
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
}

.caixa-login,
.caixa-cadastro{
    border: 1px solid var(--color_gray_dark);
    max-width: 550px;
    width: 100%;
    margin: 0 30px 40px;
    padding: 30px;
    border-radius: 8px;
}

.caixa-login fieldset,
.caixa-cadastro fieldset{
    border: 0;
    padding: 0;
}

.caixa-login fieldset{
    margin-top: 10px;
}

.caixa-cadastro p:first-child{
    display: block;
    margin: 0.625rem 0 1.875rem 0;
    width: 100%;
}

.caixa-cadastro fieldset input{
    width: 100%;
    display: block;
}

.caixa-cadastro fieldset .botao-novo-cadastro{
    margin-top: 1.875rem;
}

@media (max-width: 767px) {

    .caixa-login,
    .caixa-cadastro{
        max-width: 100%;
    }

    .page-login .page-content .board{
        flex-direction: column;
    }

    .caixa-login, .caixa-cadastro{
        margin: 20px 0;
    }
}


/* Register page */

.obriga.red {
    font-size:0
}

.obriga.red::before {
    content:'*';
    font-size:.75rem
}

.page-register .board h1{
    text-align: center;
    margin: 0 0 1.25rem;
    color: var(--color_font_dark)
}

.page-register #frm2{
    text-align: center;
}
.page-register #frm2 fieldset{
    text-align: left;
}

.page-register #CadastroAbas{
    text-align: center;
    margin: 0 0 20px;
}

.page-register #CadastroAbas li{
    display: inline-block;
    margin: 0 10px 5px;
}

.page-register #CadastroAbas li a{
    border: 1px solid var(--color_gray_dark);
    font-size: .875rem;
    width: 137px;
    text-align: center;
    -webkit-transition: ease-in-out .3s;
    -o-transition: ease-in-out .3s;
    transition: ease-in-out .3s;
    display: inline-block;
    line-height: 40px;
    border-radius: 3px;
}

.page-register #CadastroAbas li.aberta a{
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    border-color: var(--color_secondary_medium);
}

.page-register h2{
    font-size: 1rem;
    text-align: center;
}

.page-register .page-content fieldset{
    border: 0;
    margin: 20px auto 0;
    width: 100%;
    max-width: 450px;
    border: 1px solid var(--color_gray_dark);
    padding: 20px;
    font-size: .938rem;
    border-radius: 8px;
}

.page-register .topBorder + .board{
    text-align: center;
    margin: 20px 0 0;
}

#juridica,
#fisica{
    margin: 20px 0 0;
}

.page-register .page-content fieldset label{
    display: block;
    margin: 5px 0 0;
}

.page-register .page-content fieldset strong,
.page-register .page-content fieldset b{
    font-weight: 600;
}

.page-register fieldset input[type="tel"],
.page-register fieldset input[type="email"],
.page-register fieldset input[type="password"],
.page-register fieldset select,
.page-register fieldset input[type="text"]{
    margin: 4px 0 0;
    transition: ease-in-out .3s;
}

.page-register fieldset input[type="tel"]:focus,
.page-register fieldset input[type="email"]:focus,
.page-register fieldset input[type="password"]:focus,
.page-register fieldset input[type="text"]:focus{
    box-shadow: 0 2px 5px rgba(0,0,0,.1);
}

.page-register fieldset input[size="50"],
.page-register fieldset input[size="40"]{
    width: 100%;
}

fieldset{
    border: 0;
}

.blocoAlerta,
.blocoAlerta,
.cart-preview .cart-preview-item-error{
    padding: 0.5rem;
    font-size: 0.75rem;
    text-align: center;
    border-radius: 4px;
    color: #e15656;
    background-color: #ffebeb;
    margin: 5px 0;
    display: block;
}

#comentario_cliente .blocoAlerta{
    text-align: center;
}

.mensagensErro{
    color: #da0808;
    font-size: .75rem;
    padding: 4px 0 4px;
    display: inline-block;
}

@media (max-width: 480px){
    .page-register .page-content fieldset{
        border: 0;
        padding: 0;
    }
}


/* panel */

#lightwindow,
#lightwindow_overlay{
    display: none;
}

.sidebar-central{
    width: 250px;
    margin-right: 30px;
}

.sidebar-central > div > ul > li{
    margin-bottom: 1.25rem;
}

.sidebar-central h4{
    font-weight: 600;
    font-size: 1.12rem;
    margin: 10px 0 4px;
    color: var(--color_font_dark);
}

.sidebar-central a{
    font-size: .875rem;
    display: inline-block;
    margin: 4px 0;
    color: inherit;
    transition: ease-out 0.2s;
}

.sidebar-central a:hover{
    color: var(--color_primary_medium);
}

.central-menu{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    margin: 0 -10px;
}

.col-panel{
    width: calc(100% - 280px);
    font-size: .875rem;
}

.col-panel h1,
.col-panel h2{
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 10px;
    color: var(--color_font_dark);
}

.col-panel h1{
    border-bottom: solid 1px var(--color_gray_dark);
    padding-bottom: 0.625rem;
}

.col-panel .icoPai{
    width: 100%;
    margin: 20px 0 0;
    padding: 0 10px;
}

.col-panel .icoFilho{
    padding: 0 10px;
    display: inline-block;
    text-align: center;
}

.col-panel hr{
    display: none;
}

.icoFilho .bgcolor{
    background: var(--color_font_medium);
}

@media (max-width: 550px) {
    form[action*="central_premiacao_historico"] input{
        width: 100%;
        display: block;
        max-width: 300px;
        margin-bottom: 10px;
    }
}

.col-panel .icoPai img{
    display: none;
}

.central-icons{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
}

.central-icons a{
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1;
    display: block;
    padding-bottom: 20px;
    position: relative;
    z-index: 2;
    width: 100%;
    text-align: center;
}

.central-icons p{
    margin-top: -15px;
}

.col-panel .tablePage{
    border: 1px solid #e4e4e4;
}

.col-panel strong,
.col-panel b{
    font-weight: 600;
}

.col-panel .tablePage th,
.col-panel .tablePage td{
    border: 1px solid #e4e4e4;
    padding: 5px;
}

.table-overflow{
    overflow: auto;
    width: 100%;
}

@media (max-width: 991px){
    .col-panel{
        width: 100%;
        margin: 20px 0;
    }

    .sidebar-central{
        width: 100%;
        margin: 0;
    }

    .line-panel{
        display: block;
    }
}


/* Contact page */

.page-contact .page-content {
    opacity: 0
}

.page-contact .page-content.active {
    opacity: 1
}

.page-contact h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--color_font_dark);
}

.page-contact h1 + p.description{
    margin-bottom: 50px;
    font-size: 1.125rem;
}

.page-contact .page-content h3 {
    margin: 0 0 0.625rem 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--color_font_dark);
}

.page-contact .board > p {
    font-size: 1rem;
    margin: 0 0 24px;
    line-height: 1.38
}

.page-contact .cols {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.page-contact .cols .box-form {
    width: 60%;
    margin-right: 5%;
}

.page-contact .cols .info-form {
    width: 35%;
    padding-left: 30px;
}

.page-contact .cols .info-form .board{
    margin-bottom: 1.875rem;
    font-size: 0.875rem;
}

.page-contact .page-content .cols~* {
    display: none
}

.page-contact input[type=image] {
    display: none
}

.email-texto a{
    display: block;
    transition: ease-out 0.2s;
}

.info-form a{
    transition: ease-out 0.2s;
}

.info-form a:hover{
    color: var(--color_primary_medium);
}

.page-contact .page-content label.block {
    display: block;
    margin: 0 0 20px
}

.page-contact .page-content .block span.block {
    display: block;
    margin: 0 0 4px;
    color: #313438;
    font-weight: 500;
    font-size: .875rem
}

.page-contact .page-content label.block>span:not([class]) {
    display: none
}

.page-contact .page-content input.text {
    height: 42px
}

.page-contact .page-content .textarea,.page-contact .page-content input.text {
    width: 100%;
    max-width: 100%;
}

.page-contact .page-content .box-captcha {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.page-contact .page-content .box-captcha #captcha-loader {
    margin-top: 8px
}

.page-contact .page-content input#texto_captcha {
    width: calc(100% - 126px);
    margin-left: 20px
}

.page-contact .board iframe {
    width: 100%
}

.page-contact .block {
    display: block
}

.contato-telefones .block {
    font-weight: 400;
}

.contato-telefones .block::before{
    font-family: "go" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    vertical-align: middle;
    margin-right: 5px;
    color: var(--color_primary_medium);
}

.contato-telefones .block:nth-child(1)::before{
    content: '\ea0e';
    font-size: 1.75rem;
}

.contato-telefones .block:nth-child(2)::before{
    content: '\ea1a';
    font-size: 1.5625rem;
}

.page-contact .blocoSucesso {
    color: #28be48!important;
    padding: 10px 0
}

.page-contact .board .botao-commerce {
    width: 100%;
    height: 42px;
    max-width: 180px;
    padding: 0;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
}

.page-contact .page-content .msg-obriga {
    display: block;
    margin: 5px 0 20px
}

@media (max-width: 767px) {

    .page-contact h1{
        font-size: 1.25rem;
    }

    .page-contact h1 + p.description{
        font-size: 1rem;
    }

    .page-contact .cols {
        display:block
    }

    .page-contact .cols .box-form {
        width: 100%;
        padding: 0
    }

    .page-contact .cols .info-form {
        width: 100%;
        margin-top: 2.5rem;
        padding-top: 1.875rem;
        padding-left: 0;
        border-top: solid 1px var(--color_gray_dark);
    }

    .page-contact .board .botao-commerce {
        max-width: 100%;
    }

}


/* Footer */

.footer{
    background: var(--color_footer_bg);
    color: var(--color_footer_second_font);
    margin-top: 1.875rem;
    padding: 20px 0 28px;
}

.footer .footer-main .logo img{
    display: block;
    height: 100%;
    max-height: 64px;
}

.footer .footer-main .social-media a{
    font-size: 22px;
    color: var(--color_footer_highlight);
    transition: ease-out 0.2s;
    margin: 0 0.625rem;
}

.footer .footer-main .social-media a:hover{
    opacity: .8;
}

.footer .footer-main .newsletter{
    width: 100%;
    padding: 40px 0;
}

.footer .footer-main .newsletter .info{
    width: 50%;
}

.footer .footer-main .newsletter .text .first{
    font-weight: 700;
    text-transform: uppercase;
    color: var(--color_footer_main_font);
}

.footer .footer-main .newsletter .text .last{
    font-weight: 600;
    color: var(--color_footer_second_font);
}

.footer .footer-main .newsletter .form{
    width: 50%;
    padding-left: 1.25rem;
    position: relative;
}

.footer .footer-main .newsletter .form .field{
    height: 56px;
    background-color: var(--color_footer_details);
    color: var(--color_footer_second_font);
    font-weight: 600;
    width: 100%;
    padding: 1.0625rem 145px 1.0625rem 1.25rem;
    border-radius: 4px;
    font-size: .875rem;
}

.footer .footer-main .newsletter .form .field:focus{
    box-shadow: inset 0 0 4px var(--color_header_second_font);
}

.footer .footer-main .newsletter .form .field::placeholder{
    color: var(--color_footer_second_font);
}

.footer .footer-main .newsletter .form .news-button{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 130px;
    height: 39px;
    position: absolute;
    right: 6px;
    top: calc(50% - 19px);    
    font-size: .875rem;
    background-color: var(--color_footer_button);
    color: var(--color_footer_bg);
    border-radius: 4px;
    transition: ease-out 0.2s;
    font-weight: 700;
    text-transform: uppercase;
    font-family: var(--font_family);
}

.footer .footer-main .newsletter .form .news-button .icon{
    margin-left: 5px;
}

.footer .footer-main .newsletter .form .news-button:hover{
    opacity: .8;
}

@media (max-width: 500px){
    .footer .footer-main .newsletter .form .field{
        font-size: 16px;
    }
}

.footer .cols .container {
    border-top: 1px solid var(--color_footer_details);
    padding-top: 40px;
}

.footer .logo-box img{
    max-height: 74px;
    width: auto;
    height: auto;
}

.footer .social-media{
    margin-top: 20px;
}

.footer .social-media .icon{
    margin-right: 18px;
    transition: ease-out 0.2s;
}

.footer .title{
    text-transform: uppercase;
    color: var(--color_footer_main_font);
    font-size: .87rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.footer .payment-list + .title{
    margin-top: 15px;
}

.footer .list a{
    font-size: .87rem;
    font-weight: 400;
    display: block;
    line-height: 1.5;
    padding: 5px 0;
    transition: ease-out 0.2s;
}

.footer .box-infos a{
    padding-bottom: 15px;
}

.footer .social-media .icon:hover,
.footer .social-media .icon:hover svg{
    opacity: .8;
}

.footer .list a:hover{
    color: var(--color_primary);
}

.footer .list .icon,
.footer .social-media .icon{
    color: var(--color_footer_highlight);
    font-size: 25px;
}

.footer .social-media .icon-tiktok,
.footer .social-media .icon svg{
    fill: var(--color_footer_highlight);
}

@media (min-width: 768px){
    .footer .payment-list,
    .footer .security-seals{
        max-width: 192px;
    }
}

.footer .payment-list li img{
    display: block;
    width: 42px;
    height: 27px;
    opacity: 0;
    visibility: hidden;
    transition: ease-in-out 0.2s;
    display: block;
    margin: 0 6px 6px 0;
}

.footer .payment-list li img.loaded{
    opacity: 1;
    visibility: visible;
}

.footer .security-seals li{
    margin: 0 6px 6px 0;
}

.google-seal a{
    font-size: 28px;
    display: flex;
    align-items: center;
}

.google-seal .icon{
    display: block;
}

.google-seal .icon-shield{
    color: #2AA647;
    margin-right: 2px;
}

.footer .copy{
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    padding: 3rem 0 1.25rem;
    color: var(--color_footer_main_font);
}

.footer .copy .tray{
    margin-top: 1rem;
}

.footer .copy .tray .mode-preview{
    transition: ease-out 0.2s;
}

.footer .copy .tray .mode-preview:hover{
    opacity: 1;
}

#NavLogoTray{
    text-align: center;
    font-size: 0.875rem;
}

#NavLogoTray a{
    transition: ease-out 0.2s;
    font-weight: 600;
}

#NavLogoTray a:hover{
    opacity: .8;
}

#NavLogoTray .timelogotray{
    display: none;
}

@media (min-width: 1000px) and (max-width: 1230px){

    .footer .footer-main .newsletter .info{
        width: 60%;
    }

    .footer .footer-main .newsletter .form{
        width: 40%;
    }

}

@media (min-width: 768px) and (max-width: 1000px){

    .footer .footer-main .container{
        flex-wrap: wrap;
        justify-content: space-evenly;
    }

    .footer .footer-main .newsletter{
        max-width: 100%;
        margin-top: 1.875rem;
    }    

}

@media (min-width: 575px) and (max-width: 767px){

    .footer .footer-main .newsletter .info{
        justify-content: center;
    }

}

@media (max-width: 767px){    

    .footer .footer-main .logo{
        margin-bottom: 3.125rem;
    }

    .footer .footer-main .newsletter {
        flex-direction: column;
        max-width: 100%;
    }

    .footer .footer-main .newsletter .info{
        width: 100%;
    }

    .footer .footer-main .newsletter .form{
        width: 100%;
        margin-top: 15px;
        padding: 0;
    }

    .footer .footer-main .newsletter .text .first{
        font-size: 0.8125rem;
    }

    .footer .footer-main .newsletter .text .last{
        font-size: 0.8125rem;
    }

    .footer .box{
        margin-bottom: 25px;
        width: 100%;
    }

    .footer .logo-box{
        margin: 0 auto 35px;
        text-align: center;
    }

    .footer .social-media{
        justify-content: center;
    }

    .footer .social-media .icon {
        margin: 0 9px;
    }

    .footer .list a{
        padding: 10px 0;
    }

}


/* Lista presentes */

.lista_presentes table{
    font-size: 0.875rem;
}

.lista_presentes strong,
.lista_presentes b{
    font-weight: 600;
}

.lista_presentes th{
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.6rem;
}

.lista_presentes td{
    padding: 0.6rem;
}

.lista_presentes select.select{
    width: 100%;
    margin-bottom: 0.65rem;
    min-width: 295px;
}

.lista_presentes td a{
    width: 100px;
    display: block;
    float: left;
}

.lista_presentes td a img{
    max-width: 100%;
}

.lista_presentes td a + strong{
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 104px);
    font-size: 0.75rem;
    max-width: 250px;

}

.lista_presentes textarea{
    border: 1px solid #e4e4e4;
    padding: 14px;
    border-radius: 0px;
    color: #565656;
    width: 100% !important;
    display: block;
    margin-top: 0.25rem;
}

.lista_presentes li:last-child{
    margin-bottom: 0.938rem;
}

#form_presentes .relative-button{
    float: right;
}

#form_presentes .botao-commerce{
    display: inline-block;
}

@media (max-width: 991px){

    #form_presentes{
        display: block;
        max-width: 450px;
        width: 100%;
        margin: auto;
    }

    .lista_presentes li:not([class]):first-child{
        display: none;
    }

    .lista_presentes td{
        display: block;
        width: 100% !important;
    }

}

/* Afiliados */

.page-content .style1{
    font-size: 0.875rem !important;
    color: var(--color_font_medium);
}

.page-content .style4{
    font-size: 0.875rem !important;
    color: var(--color_font_medium);
}

.page-extra .table-overflow .table-overflow table tr[bgcolor="#e7e7e7"] {
    background: var(--color_gray_medium);
}

.page-extra .page-content > .container > h1 {
    background: none;
    font-size: 2em;
    height: auto;
    border: 0 none;
    color: var(--color_font_medium);
    padding-left: 0;
}

.campoform:not([type="checkbox"]){
    margin: 0.675rem 0;
}

.page-extra table[style="width: 700px"]{
    width: 100% !important;
}

@media (max-width: 767px){

    .page-extra form[action*="afiliados.php?loja="] table td{
        display: block;
    }

}

#texto-termo{
    font-size: 0.875rem;
}

.msg-aceita-termo{
    font-size: 0.875rem;
}

#concorda-termo{
    margin-top: 1.875rem;
    padding-top: 1.875rem;
    border-top: solid 1px var(--color_gray_dark);
}

#acceptTerm {
    display: block;
    margin-top: 1.25rem;
    padding: 0.5rem 0.625rem;
    font-size: .75rem;
    font-weight: 700;
    text-transform: uppercase;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    transition: 0.2s ease-out;
}

#acceptTerm:hover {
    opacity: .8;
}

.checkbox-accept-terms{
    position: relative;
    display: block;
}

.checkbox-accept-terms input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0;
}

.checkbox-accept-terms label {
    position: relative;
    display: block;
    padding-left: 1.875rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color_font_medium);
}

.checkbox-accept-terms label::before {
    position: absolute;
    content: '';
    top: 0.125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    background-color: #fff;
    border: solid 1px var(--color_gray_dark);
    border-radius: 2px;
    transition: ease-in-out 0.2s;
}

.checkbox-accept-terms label::after {
    position: absolute;
    content: '';
    top: 0.125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    background: no-repeat 50%/50% 50%;
}

.checkbox-accept-terms input:checked ~ label::before {
    color: var(--color_font_inverted);
    border-color: var(--color_secondary_medium);
    background-color: var(--color_secondary_medium);
}

.checkbox-accept-terms input:checked ~ label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

@media (max-width: 767px){
    .compare-buttons{
        margin: 5px 0;
    }
}

.progressive{
    margin: 10px 0;
    font-size: 0.75rem;
    text-align: center;
}

.banner-header img{
    display: block;
    margin: auto;
    max-width: 100%;
}

@media (max-width: 1024px){
    .banner-header{
        display: none;
    }
}

#ProdAbas li{
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--color_font_dark);
    text-align: center;
    margin-bottom: 40px;
}

.page-noticia #ProdBlock.prodBox {
    display: block!important;
    margin-top: 40px;
}

#listaNoticiaProdutos{
    display: flex;
    flex-wrap: wrap;
}

#listaNoticiaProdutos li{
    width: 25%;
    position: relative;
}

#produto_imagem{
    float: left;
}
#produto_imagem ~ *{
    margin-left: 100px;
}

#produto_imagem img{
    max-width: 90px;
}

#produto_comprar{
    font-size: 0.875rem;
}

#produto_comprar a img{
    display: none;
}

#produto_comprar a::before{
    content: "Ver detalhes";
    padding: 7px 14px;
    font-weight: 700;
    font-size: 0.75rem;
    text-transform: uppercase;
    border-radius: 3px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    display: inline-block;
    margin: 5px 0 6px;
    transition: ease-out .2s;
}

#produto_comprar a:hover::before{
    opacity: .9;
}

#produto_comprar a::after{
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}


#produto_comprar{
    font-size: 0;
}

#produto_comprar .precoAvista{
    font-weight: 700;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--color_primary_medium);
    font-size: 14px;
}

#produto_dados h3{
    color: var(--color_font_dark);
    font-size: 0.875rem;
    margin: 10px 0;
    line-height: 1.3;
    display: block;
    font-weight: 500;
}

#produto_dados p{
    display: none;
}

@media (max-width: 1200px) {
    #listaNoticiaProdutos li{
        width: 33.33333%;
        margin-bottom: 20px;
    }

}

@media (max-width: 767px) {
    #listaNoticiaProdutos li{
        width: 50%;
    }

}

@media (max-width: 480px) {
    #listaNoticiaProdutos li{
        width: 100%;
    }

}

#ProdBlock{
    display: none !important;
}

#tipos-listas img{
    max-width: 100%;
    margin: 0 auto;
    display: block;
}

form .busca input[type="text"]{
    border: 1px solid var(--color_gray_dark);
    line-height: 35px;
    padding: 0 5px;
    margin-right: 10px;
}


/* Custom pages with title */

.is-custom-page .page-title{
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
    margin-bottom: 30px;
}

.is-custom-page .page-title .text{
    display: block;
}

@media (max-width: 575px){
    .is-custom-page .page-title{
        font-size: 1.25rem;
    }
}


/* Newsletter page */

.page-newsletter .page-content{
    opacity: 0;
    visibility: hidden;
    transition: ease-out 0.2s;
}

.page-newsletter .page-content.show{
    opacity: 1;
    visibility: visible;
}

.page-newsletter .board h1{
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
}

.page-newsletter .formulario-newsletter p:first-child{
    margin-bottom: 1.875rem;
}

.page-newsletter .formulario-newsletter p:first-child br{
    display: none;
}

.page-newsletter .formulario-newsletter .box-captcha,
.page-newsletter .formulario-newsletter .box-captcha-newsletter{
    display: flex;
}

.page-newsletter .formulario-newsletter .box-captcha input,
.page-newsletter .formulario-newsletter .box-captcha-newsletter input{
    height: 36px;
    min-width: 200px;
    margin-left: 10px;
    padding: 0 15px;
}

.page-newsletter .formulario-newsletter .botao-commerce{
    text-transform: uppercase;
    font-weight: 600;
    margin-top: 1rem;
    display: block;
}

.page-newsletter .page-content.success-message-newsletter .board p:first-child {
    max-width: 600px;
    margin: 0 auto;
    text-align: justify;
}

.page-newsletter .page-content.success-message-newsletter .board p:first-child br{
    display: none;
}

.page-newsletter .page-content.success-message-newsletter .board p:first-child a{
    display: block;
    margin: 30px auto 0;
    width: 225px;
    text-align: center;
}

@media (max-width:  767px){

    .page-newsletter .board h1 {
        font-size: 1.25rem;
    }

    .page-newsletter .page-content.success-message-newsletter .board p{
        text-align: center;
    }

}

@media (max-width:  575px){

    .page-newsletter .formulario-newsletter .box-captcha input{
        width: calc(100% - 115px);
    }

    .page-newsletter .formulario-newsletter .botao-commerce{
        width: 100%;
    }

    .page-newsletter .page-content.success-message-newsletter .board p:first-child a{
        width: 100%;
    }

}

@media screen and (-webkit-min-device-pixel-ratio:0) { 
    select:focus,
    textarea:focus,
    input:focus {
        font-size: 16px;
    }
}

@media screen and (-webkit-min-device-pixel-ratio:0) { 
    select,
    textarea,
    input {
        font-size: 16px;
    }
}

.page-product .product-additional-fields{
    margin-bottom: 1.25rem;
}

.page-product .product-additional-fields #menuVars,
.page-product .product-variants #menuVars{
    text-align: center;
}

.page-product .productAdditionalInformation .dd{
    position: relative;
}

.page-product .productAdditionalInformation .dd .ddTitle{
    overflow: hidden;
    cursor: default;
    padding: 0.5rem;
    border: 1px solid #c3c3c3;
    width: 200px;
}

.page-product .productAdditionalInformation .dd .ddTitle .ddTitleText{
    float: left;
    font-size: 12px;
}

.page-product .productAdditionalInformation .dd .ddTitle span.arrow{
    background: url(/assets/store/js/msdropdown/dd_arrow.gif) no-repeat 0 0;
    float: right;
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.page-product .productAdditionalInformation .dd .ddChild{
    position: absolute;
    border: 1px solid #999;
    display: none;
    margin: 0;
    width: 200px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    background-color: #f2f2f2;
    height: 400px;
}

.page-product .productAdditionalInformation .dd .ddChild a{
    font-weight: bold;
    display: block;
    padding: 3px 0 3px 3px;
    text-decoration: none;
    color: #000;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    width: 100% !important;
    font-family: Arial,Helvetica,sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #666;
}

.page-product .productAdditionalInformation .dd .ddChild a.selected{
    background-color: #c3c3c3;
    font-family: Arial,Helvetica,sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #666;
}

.page-product .productAdditionalInformation .dd .ddChild a img{
    border: 0;
    vertical-align: middle;
    height: auto;
    max-width: 100%;
}

.cart-preview .related-product-title,
.cart-preview .related-product-content *{
    font-family: var(--font_family);
}

.cart-preview .related-product-slide-content .buy-link,
.cart-preview .related-product-slide-content .buy-link:hover,
.cart-preview .related-product-slide-content .buy-link:focus, 
.cart-preview .related-product-slide-content .buy-link:active {
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    border: 1px solid var(--color_secondary_medium);
    transition: ease-out 0.2s;
}

.cart-preview .related-product-slide-content .buy-link:hover {
    opacity: .8;
}