{% element 'snippets/banner-home' %}

{# First showcase #}
{% element 'showcase' {
    'rand'     : settings.showcase_rand_products,
    'type'     : settings.showcase_type_1,
    'quantity' : settings.showcase_quantity_1
} %}

{% element 'snippets/banner-grid' %}    

{# Second showcase #}
{% element 'showcase' {
    'rand'     : settings.showcase_rand_products,
    'type'     : settings.showcase_type_2,
    'quantity' : settings.showcase_quantity_2
} %}

{% element 'snippets/banner-line' %}

{% for page in pages.custom %}
    {% if page.slug == "noticias" and settings.show_news %}
        <div class="section-news slide-has-dots">
            <div class="container">
                <div class="section-header">
                    <h2 class="title-section">
                        Not&iacute;cias
                    </h2>
                </div>
                <div class="news-content swiper-container hide-dots-desk">
                    <div class="swiper-wrapper"></div>
                    <div class="dots"></div>
                </div>
                <a href="/noticias" class="botao-commerce flex align-center justify-center" title="Ver mais not&iacute;cias">Ver mais</a>
            </div>
        </div>
    {% endif %}
{% endfor %}

{% for pages in pages.custom %}
    {% if pages.info == "depoimentos_de_clientes" and settings.show_reviews %}
        <div class="section-avaliacoes slide-has-dots">
            <div class="container">
                <div class="section-header">
                    <h2 class="title-section">
                        Depoimentos
                    </h2>
                </div>
                {% element 'CustomerReview.reviews' %}
            </div>
        </div>
    {% endif %}
{% endfor %}