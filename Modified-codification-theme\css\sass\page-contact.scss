/**
* Page Contact
**/

.page-contact{
    
    .header.fixed{
        position: relative;
    }
    
    .page-content{
        
        > *{
            background: #fff;
            padding: 15px;
        }
        
        h1{
            color: #3d4445;
            font: 400 26px/26px $font;
            margin: 0;
            text-transform: none;
        }
        
        .formulario-contato button,
        .formulario-contato input,
        .formulario-contato textarea{
            font-size: 1.6rem;
            margin-top: 3px;
            padding: 7px;
        }
        
        #texto_captcha{
            width: 130px;
        }
        
        .Seguro,
        .carrinho-heading,
        .msg-obriga{
            display: none;
        }
        
        .contato-telefones{
            
            span{
                display: block;
                font-size: 2rem;
                padding: 7px 0;
            }
        }
        
        .container2{
            padding: 30px;
            
            @media screen and (max-width: $sm){
                padding: 0;
            }
            
            .container2{
                padding: 0;
            }
            
            h3{
                color: #3d4445;
                font: 400 18px/18px $font;
                margin: 15px 0 5px;
                text-transform: none;
            }
            
            h3 + span,
            h3 + p{
               color: #666;
               font: 400 13px/13px $font;
               
               strong{
                   font-weight: 400;
               }
               
               a{
                   color: #666;
                   font: 400 18px/18px $font;
               }
            }
            
            .tit-contato{
                border-top: 1px solid #e9e9e9;
                font-size: 24px;
                line-height: 24px;
                margin-top: 50px;
                padding-top: 20px;
            }
        }
        
        > .board{
            padding: 0 45px;
            
            @media screen and (max-width: $sm){
                padding: 0 15px;
            }
            
            p{
                border-bottom: 1px solid #e9e9e9;
                color: #666;
                font: 400 13px/15px $font;
                padding-bottom: 15px;
            }
        }
    }
    
    .block {
        color: #666;
        font: 400 11px/11px $font;
    }
    
    .formulario-contato{
        padding-bottom: 30px;
        
        #nome_contato{
            width: 573px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #empresa{
            width: 457px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #email_contato{
            width: 394px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #telefone_contato{
            width: 200px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #assunto{
            width: 200px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #mensagem_contato{
            min-height: 190px;
            width: 573px;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        #imagem{
            background: url('../img/send-button.png') no-repeat left top;
            height: 0;
            padding: 20px 65px;
            width: 0;
        }
    }
    
    .topBorder + .container2 + .board .block {
        font-size: 1.2rem;
        line-height: 1.6rem;
    }
    
    @media screen and (max-width: $sm){
        
        .page-content{
            padding: 15px;
        }
    }
}