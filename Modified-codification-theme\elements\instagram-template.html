{% if settings.feed_instagram_active or settings.feed_instagram_auto_active %}
<div class="template-instagram">
    {% if settings.feed_instagram_title %}
    <div class="section-header">
        <h2 class="title-section">
            {% if settings.feed_instagram_link %}
                <a href="{{ settings.feed_instagram_link }}" {{ settings.feed_instagram_link_blank ? 'target="_blank" rel="noopener noreferrer"'}}>
                    {{ settings.feed_instagram_title }}
                </a>
            {% else %}
                {{ settings.feed_instagram_title }}
            {% endif %}
        </h2>
    </div>
    {% endif %}
    
    {% if settings.feed_instagram_auto_active %}
    <div class="container">
        {{ settings.feed_instagram_auto_textarea }}
    </div>
    {% endif %}
    
    {% if settings.feed_instagram_active %}
    <div class="container swiper-container">
        <div class="swiper-wrapper" id="instafeed">
            {% for i in 1..8 %}
            
                {% set link = attribute(settings, 'link_instagram_template_'~i) %}
                {% set blank = attribute(settings, 'link_blank_instagram_template_'~i) %}
                {% set image = attribute(settings, 'image_instagram_template_'~i) %}
            
                {% if image %}
                <div class="item swiper-slide">
                    <a {% if link %} href="{{ link }}" {% endif %} {{ blank ? 'target="_blank" rel="noopener noreferrer"'}}>
                        <div >
                            <img loading="lazy" class="lazyload" data-src="{{ asset(image) }}" alt="Postagem do Instagram"/>
                        </div>
                    </a>
                </div>
                {% endif %}
            {% endfor %}
        </div>
        <div class="prev">
            <i class="icon icon-arrow-left"></i>
        </div>
        <div class="next">
            <i class="icon icon-arrow-right"></i>
        </div>
        <div class="dots"></div>
    </div>
    {% endif %}
    
</div>
{% endif %}