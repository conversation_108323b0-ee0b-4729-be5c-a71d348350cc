<!-- <PERSON><PERSON> do Rodapé -->
{% if settings.footer_column_title_1 or settings.footer_column_title_2 or settings.footer_column_title_3 %}
    <!-- Menus Customizados -->
    {% if settings.footer_column_title_1 %}
        <div class="box box-custom-menu">
            <div class="title">{{ settings.footer_column_title_1 }} <i class="icon icon-arrow-down"></i></div>
            <div class="overflow">
                <ul class="list">
                    {% if settings.footer_link_title_1_1 and settings.footer_link_url_1_1 %}
                        <li>
                            <a href="{{ settings.footer_link_url_1_1 }}" title="{{ settings.footer_link_title_1_1 }}">
                                {{ settings.footer_link_title_1_1 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_1_2 and settings.footer_link_url_1_2 %}
                        <li>
                            <a href="{{ settings.footer_link_url_1_2 }}" title="{{ settings.footer_link_title_1_2 }}">
                                {{ settings.footer_link_title_1_2 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_1_3 and settings.footer_link_url_1_3 %}
                        <li>
                            <a href="{{ settings.footer_link_url_1_3 }}" title="{{ settings.footer_link_title_1_3 }}">
                                {{ settings.footer_link_title_1_3 }}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    {% endif %}
    
    {% if settings.footer_column_title_2 %}
        <div class="box box-custom-menu">
            <div class="title">{{ settings.footer_column_title_2 }} <i class="icon icon-arrow-down"></i></div>
            <div class="overflow">
                <ul class="list">
                    {% if settings.footer_link_title_2_1 and settings.footer_link_url_2_1 %}
                        <li>
                            <a href="{{ settings.footer_link_url_2_1 }}" title="{{ settings.footer_link_title_2_1 }}">
                                {{ settings.footer_link_title_2_1 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_2_2 and settings.footer_link_url_2_2 %}
                        <li>
                            <a href="{{ settings.footer_link_url_2_2 }}" title="{{ settings.footer_link_title_2_2 }}">
                                {{ settings.footer_link_title_2_2 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_2_3 and settings.footer_link_url_2_3 %}
                        <li>
                            <a href="{{ settings.footer_link_url_2_3 }}" title="{{ settings.footer_link_title_2_3 }}">
                                {{ settings.footer_link_title_2_3 }}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    {% endif %}
    
    {% if settings.footer_column_title_3 %}
        <div class="box box-custom-menu">
            <div class="title">{{ settings.footer_column_title_3 }} <i class="icon icon-arrow-down"></i></div>
            <div class="overflow">
                <ul class="list">
                    {% if settings.footer_link_title_3_1 and settings.footer_link_url_3_1 %}
                        <li>
                            <a href="{{ settings.footer_link_url_3_1 }}" title="{{ settings.footer_link_title_3_1 }}">
                                {{ settings.footer_link_title_3_1 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_3_2 and settings.footer_link_url_3_2 %}
                        <li>
                            <a href="{{ settings.footer_link_url_3_2 }}" title="{{ settings.footer_link_title_3_2 }}">
                                {{ settings.footer_link_title_3_2 }}
                            </a>
                        </li>
                    {% endif %}
                    {% if settings.footer_link_title_3_3 and settings.footer_link_url_3_3 %}
                        <li>
                            <a href="{{ settings.footer_link_url_3_3 }}" title="{{ settings.footer_link_title_3_3 }}">
                                {{ settings.footer_link_title_3_3 }}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    {% endif %}
{% else %}
    <!-- Menus Padrão (fallback) -->
    <div class="box box-categories">
        <div class="title">Categorias <i class="icon icon-arrow-down"></i></div>
        <div class="overflow">
            <ul class="list">
                {% for category in categories %}
                    <li>
                        <a href="{{ category.link }}" title="{{ category.name }}">
                            {{ category.name }}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    
    <div class="box box-pages">
        <div class="title">Institucional <i class="icon icon-arrow-down"></i></div>
        <div class="overflow">
            <ul class="list">
                {% for custom in pages.custom %}
                <li>
                    <a href="{{ custom.url }}" title="{{ custom.name }}">{{ custom.name }}</a>
                </li>
                {% endfor %}
                <li>
                    <a href="{{ links.contact }}" title="Fale Conosco">Fale Conosco</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="box box-infos">
        <div class="title">Atendimento <i class="icon icon-arrow-down"></i></div>
        <div class="overflow">
            <ul class="list"> 
                {% if whatsappNumber %}
                <li>
                    {% element 'snippets/whatsapp' { 'number': whatsappNumber, 'include_text' : false } %}
                </li>
                {% endif %}
                
                {% if phone %}
                {% set link = phone | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                <li>
                    <a href="tel:{{ link }}" title="Telefone: {{ phone }}">
                        <i class="icon icon-phone v-align-middle"></i>
                        {{ phone }}
                    </a>
                </li>
                {% endif %}
                
                {% if email %}
                <li>
                    <a href="mailto:{{ email }}" title="Email: {{ email }}">
                        <i class="icon icon-email v-align-middle"></i>
                        {{ email }}
                    </a>
                </li>
                {% endif %}
                
                {% if Translation('ag_atendimento') %}
                <li class="hour">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                            <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z"></path>
                        </svg>
                        <span>Horário de Atendimento:</span>
                    </div>
                    <div class="text">
                        <span>{{ Translation('ag_atendimento') }}</span>
                    </div>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
{% endif %}
