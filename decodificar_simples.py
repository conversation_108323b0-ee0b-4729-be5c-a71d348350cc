#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simples para decodificar entidades HTML no arquivo settings.html
"""

import html
import chardet

def main():
    arquivo = "settings.html"

    try:
        # Detecta a codificação do arquivo
        print(f"Detectando codificacao do arquivo: {arquivo}")
        with open(arquivo, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            print(f"Codificacao detectada: {encoding}")

        # Lê o arquivo com a codificação correta
        print(f"Lendo arquivo: {arquivo}")
        with open(arquivo, 'r', encoding=encoding) as f:
            conteudo = f.read()

        # Conta quantas entidades HTML existem
        entidades_antes = conteudo.count('&')

        # Decodifica as entidades HTML
        print("Decodificando entidades HTML...")
        conteudo_decodificado = html.unescape(conteudo)

        # Conta quantas entidades HTML restaram
        entidades_depois = conteudo_decodificado.count('&')

        # Salva o arquivo decodificado
        arquivo_saida = "settings_decodificado.html"
        print(f"Salvando arquivo decodificado: {arquivo_saida}")
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            f.write(conteudo_decodificado)

        print("Processo concluido com sucesso!")
        print(f"Entidades HTML antes: {entidades_antes}")
        print(f"Entidades HTML depois: {entidades_depois}")
        print(f"Entidades decodificadas: {entidades_antes - entidades_depois}")

        # Mostra alguns exemplos de conversões
        print("\nExemplos de conversoes realizadas:")
        exemplos = [
            ("Configura&ccedil;&otilde;es", "Configurações"),
            ("T&iacute;tulo", "Título"),
            ("voc&ecirc; n&atilde;o", "você não"),
            ("Aten&ccedil;&atilde;o", "Atenção"),
            ("Se&ccedil;&atilde;o", "Seção"),
            ("Eletr&ocirc;nicos", "Eletrônicos")
        ]

        for antes, depois in exemplos:
            if antes in conteudo:
                print(f"  - {antes} -> {depois}")

    except FileNotFoundError:
        print(f"ERRO: Arquivo '{arquivo}' nao encontrado.")
        print("   Certifique-se de que o arquivo esta no mesmo diretorio do script.")
    except Exception as e:
        print(f"ERRO: {e}")

if __name__ == "__main__":
    main()
