// fonts
$font: 'Roboto', sans-serif;

// vendor prefix
@mixin prefix($name, $argument) {
    -webkit-#{$name}: #{$argument};
       -moz-#{$name}: #{$argument};
        -ms-#{$name}: #{$argument};
         -o-#{$name}: #{$argument};
            #{$name}: #{$argument};
}

// styling placeholder
@mixin placeholder {
  ::-webkit-input-placeholder {@content}
  :-moz-placeholder           {@content}
  ::-moz-placeholder          {@content}
  :-ms-input-placeholder      {@content}   
}

@mixin typo($size:12px, $w:400, $family:$font, $lh:1) {
    font-family: $family;
    font-size: $size;
    font-weight: $w;
    line-height: $lh; 
}

// default transition
.transition{
    @include prefix('transition', '300ms all ease 0s');
}

// queries
$sm: 767px;
$md: 992px;

// rem ajust
html,
body{
    @include typo(10px, 400, $font, 1);
}

.application{
    background-attachment: fixed;
    background-color: #f1f1f1;
    background-position: center top;
}

.container{
    @media (min-width: 768px) and (max-width: 991px){
        width: 95%;
    }
}

.content-split{
    border-color: #ccc;
    margin-bottom: 30px;
    margin-top: 0;
}

.page-content{
    z-index: 5;
    background-color: #fff;
    padding: 15px;
    
    .container{
        padding: 0;
        width: 100%;
    }   
}

.page-home, .page-catalog, .page-product, .page-search{
    .page-content{
        background-color: transparent;
    }
}



.site-main {
    position: relative;
    // z-index: 10;
}

/* centered columns styles */
.row-centered {
    text-align:center;
}

.col-centered{
    display: inline-block;
    float:none;
    /* reset the text-align */
    text-align:left;
    /* inline-block space fix */
    margin-right:-4px;
}

#miniad{
    width: auto;
}