#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir problemas de codificação e salvar em ISO-8859-1
"""

import html
import chardet

def corrigir_e_salvar_iso(arquivo_entrada, arquivo_saida=None):
    """
    Corrige a codificação e salva o arquivo em ISO-8859-1
    """
    if arquivo_saida is None:
        arquivo_saida = arquivo_entrada.replace('.html', '_iso.html')
    
    try:
        # Detecta a codificação atual
        print(f"Analisando arquivo: {arquivo_entrada}")
        with open(arquivo_entrada, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding_detectada = result['encoding']
            print(f"Codificacao detectada: {encoding_detectada}")
        
        # Lê o arquivo com a codificação detectada
        with open(arquivo_entrada, 'r', encoding=encoding_detectada) as f:
            conteudo = f.read()
        
        # Decodifica entidades HTML se necessário
        print("Decodificando entidades HTML...")
        conteudo_decodificado = html.unescape(conteudo)
        
        # Tenta salvar em ISO-8859-1
        print(f"Salvando em ISO-8859-1: {arquivo_saida}")
        try:
            with open(arquivo_saida, 'w', encoding='iso-8859-1') as f:
                f.write(conteudo_decodificado)
            print("Arquivo salvo com sucesso em ISO-8859-1!")
            
        except UnicodeEncodeError as e:
            print(f"AVISO: Alguns caracteres nao podem ser salvos em ISO-8859-1")
            print(f"Erro: {e}")
            print("Tentando salvar em UTF-8...")
            
            arquivo_saida_utf8 = arquivo_saida.replace('.html', '_utf8.html')
            with open(arquivo_saida_utf8, 'w', encoding='utf-8') as f:
                f.write(conteudo_decodificado)
            print(f"Arquivo salvo em UTF-8: {arquivo_saida_utf8}")
        
        # Verifica o resultado
        print("\nVerificando resultado...")
        with open(arquivo_saida, 'r', encoding='iso-8859-1') as f:
            conteudo_verificacao = f.read()
        
        # Procura por exemplos específicos
        exemplos = [
            "Atenção",
            "Configurações", 
            "Título",
            "você não",
            "Seção"
        ]
        
        print("Exemplos encontrados no arquivo corrigido:")
        for exemplo in exemplos:
            if exemplo in conteudo_verificacao:
                print(f"  OK: {exemplo}")
        
        return True
        
    except Exception as e:
        print(f"ERRO: {e}")
        return False

def main():
    arquivo = "settings_novo.html"
    
    print("=== Corretor de Codificacao ===")
    print("Este script vai:")
    print("1. Detectar a codificacao atual do arquivo")
    print("2. Decodificar entidades HTML")
    print("3. Salvar em ISO-8859-1")
    print()
    
    sucesso = corrigir_e_salvar_iso(arquivo, "settings_iso.html")
    
    if sucesso:
        print("\n=== PROCESSO CONCLUIDO ===")
        print("Arquivo corrigido: settings_iso.html")
        print("Codificacao: ISO-8859-1")
    else:
        print("\n=== ERRO NO PROCESSO ===")

if __name__ == "__main__":
    main()
