#!/usr/bin/env python3
"""
File Encoding Analyzer Tool
Analyzes the directory structure and detects the encoding of each file.
"""

import os
import chardet
import argparse
from pathlib import Path
from typing import Dict, <PERSON>, Tuple, Optional


class FileEncodingAnalyzer:
    def __init__(self, target_directory: str):
        self.target_directory = Path(target_directory)
        self.results: Dict[str, List[Tuple[str, str]]] = {}
        
    def detect_file_encoding(self, file_path: Path) -> str:
        """
        Detect the encoding of a file using chardet library.
        
        Args:
            file_path: Path to the file
            
        Returns:
            String representation of the detected encoding
        """
        try:
            # Read a sample of the file to detect encoding
            with open(file_path, 'rb') as file:
                raw_data = file.read(10000)  # Read first 10KB for detection
                
            if not raw_data:
                return "empty"
                
            # Use chardet to detect encoding
            result = chardet.detect(raw_data)
            
            if result['encoding']:
                confidence = result['confidence']
                encoding = result['encoding'].lower()
                
                # Add confidence level for uncertain detections
                if confidence < 0.7:
                    return f"{encoding} (confidence: {confidence:.2f})"
                else:
                    return encoding
            else:
                return "unknown"
                
        except (<PERSON><PERSON>rror, OSError, UnicodeDecodeError) as e:
            return f"error: {str(e)}"
        except Exception as e:
            return f"detection_error: {str(e)}"
    
    def is_text_file(self, file_path: Path) -> bool:
        """
        Determine if a file is likely a text file based on extension.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if the file is likely a text file
        """
        text_extensions = {
            '.txt', '.html', '.htm', '.css', '.js', '.json', '.xml', '.svg',
            '.md', '.py', '.php', '.java', '.c', '.cpp', '.h', '.hpp',
            '.cs', '.vb', '.sql', '.yaml', '.yml', '.ini', '.cfg', '.conf',
            '.log', '.csv', '.tsv', '.bat', '.sh', '.ps1', '.rb', '.go',
            '.rs', '.swift', '.kt', '.scala', '.pl', '.r', '.m', '.mm'
        }
        
        return file_path.suffix.lower() in text_extensions
    
    def analyze_directory(self) -> None:
        """
        Recursively analyze the target directory and detect file encodings.
        """
        if not self.target_directory.exists():
            print(f"Error: Directory '{self.target_directory}' does not exist.")
            return
            
        print(f"Analyzing directory: {self.target_directory}")
        print("=" * 60)
        
        # Walk through all directories and files
        for root, dirs, files in os.walk(self.target_directory):
            root_path = Path(root)
            relative_root = root_path.relative_to(self.target_directory)
            
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            if files:
                folder_key = str(relative_root) if str(relative_root) != '.' else 'root'
                self.results[folder_key] = []
                
                for file in sorted(files):
                    # Skip hidden files
                    if file.startswith('.'):
                        continue
                        
                    file_path = root_path / file
                    
                    if self.is_text_file(file_path):
                        encoding = self.detect_file_encoding(file_path)
                    else:
                        encoding = "binary"
                    
                    self.results[folder_key].append((file, encoding))
    
    def print_results(self) -> None:
        """
        Print the analysis results in a tree-like structure.
        """
        if not self.results:
            print("No files found or analyzed.")
            return
            
        print("\nFile Structure and Encoding Analysis:")
        print("=" * 60)
        
        # Sort folders for consistent output
        sorted_folders = sorted(self.results.keys())
        
        for folder in sorted_folders:
            if folder == 'root':
                print("\\")
            else:
                # Format folder path with backslashes for Windows-style display
                folder_display = folder.replace('/', '\\')
                print(f"\\{folder_display}")
            
            files = self.results[folder]
            for file_name, encoding in files:
                print(f"   {file_name}  ({encoding})")
            
            print()  # Empty line between folders
    
    def save_results_to_file(self, output_file: str) -> None:
        """
        Save the analysis results to a text file.
        
        Args:
            output_file: Path to the output file
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"File Structure and Encoding Analysis\n")
                f.write(f"Target Directory: {self.target_directory}\n")
                f.write("=" * 60 + "\n\n")
                
                sorted_folders = sorted(self.results.keys())
                
                for folder in sorted_folders:
                    if folder == 'root':
                        f.write("\\\n")
                    else:
                        folder_display = folder.replace('/', '\\')
                        f.write(f"\\{folder_display}\n")
                    
                    files = self.results[folder]
                    for file_name, encoding in files:
                        f.write(f"   {file_name}  ({encoding})\n")
                    
                    f.write("\n")
                    
            print(f"Results saved to: {output_file}")
            
        except Exception as e:
            print(f"Error saving results to file: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Analyze file structure and detect encoding of files in a directory"
    )
    parser.add_argument(
        "directory",
        help="Target directory to analyze"
    )
    parser.add_argument(
        "-o", "--output",
        help="Output file to save results (optional)"
    )
    
    args = parser.parse_args()
    
    # Create analyzer instance
    analyzer = FileEncodingAnalyzer(args.directory)
    
    # Perform analysis
    analyzer.analyze_directory()
    
    # Print results
    analyzer.print_results()
    
    # Save to file if requested
    if args.output:
        analyzer.save_results_to_file(args.output)


if __name__ == "__main__":
    main()
