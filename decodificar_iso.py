#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para decodificar entidades HTML mantendo a codificação ISO-8859-1
"""

import html
import chardet

def decodificar_mantendo_iso(arquivo_entrada, arquivo_saida=None):
    """
    Decodifica entidades HTML mantendo a codificação original do arquivo
    """
    if arquivo_saida is None:
        arquivo_saida = arquivo_entrada.replace('.html', '_decodificado.html')
    
    try:
        # Detecta a codificação atual
        print(f"Analisando arquivo: {arquivo_entrada}")
        with open(arquivo_entrada, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding_original = result['encoding']
            print(f"Codificacao detectada: {encoding_original}")
        
        # Lê o arquivo com a codificação original
        with open(arquivo_entrada, 'r', encoding=encoding_original) as f:
            conteudo = f.read()
        
        # Conta entidades antes
        entidades_antes = conteudo.count('&')
        print(f"Entidades HTML encontradas: {entidades_antes}")
        
        # Decodifica entidades HTML
        print("Decodificando entidades HTML...")
        conteudo_decodificado = html.unescape(conteudo)
        
        # Conta entidades depois
        entidades_depois = conteudo_decodificado.count('&')
        
        # Salva com a mesma codificação original
        print(f"Salvando com codificacao {encoding_original}: {arquivo_saida}")
        with open(arquivo_saida, 'w', encoding=encoding_original) as f:
            f.write(conteudo_decodificado)
        
        print("Arquivo salvo com sucesso!")
        print(f"Entidades HTML antes: {entidades_antes}")
        print(f"Entidades HTML depois: {entidades_depois}")
        print(f"Entidades decodificadas: {entidades_antes - entidades_depois}")
        
        # Testa alguns exemplos
        print("\nTestando algumas conversoes:")
        exemplos_teste = [
            ("Configura&ccedil;&otilde;es", "Configurações"),
            ("T&iacute;tulo", "Título"),
            ("voc&ecirc; n&atilde;o", "você não"),
            ("Aten&ccedil;&atilde;o", "Atenção"),
            ("Se&ccedil;&atilde;o", "Seção")
        ]
        
        for antes, depois in exemplos_teste:
            if antes in conteudo and depois in conteudo_decodificado:
                print(f"  {antes} -> {depois}")
        
        return True
        
    except Exception as e:
        print(f"ERRO: {e}")
        return False

def main():
    arquivo = "settings.html"  # Arquivo original
    
    print("=== Decodificador HTML para ISO-8859-1 ===")
    print("Este script vai:")
    print("1. Ler o arquivo original em sua codificacao nativa")
    print("2. Decodificar entidades HTML")
    print("3. Salvar mantendo a codificacao original")
    print()
    
    sucesso = decodificar_mantendo_iso(arquivo, "settings_final.html")
    
    if sucesso:
        print("\n=== PROCESSO CONCLUIDO ===")
        print("Arquivo final: settings_final.html")
        print("Codificacao: Mantida a original (ISO-8859-1)")
        print("\nO arquivo esta pronto para uso!")
    else:
        print("\n=== ERRO NO PROCESSO ===")

if __name__ == "__main__":
    main()
