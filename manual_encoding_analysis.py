#!/usr/bin/env python3
"""
Manual Encoding Analysis
Based on the visual comparison of both reports, identify files that need correction.
"""

def analyze_encoding_differences():
    """
    Manual analysis based on the comparison of both encoding reports
    """
    
    print("=" * 80)
    print("ENCODING CORRECTION ANALYSIS")
    print("Current-codification-theme vs Modified-codification-theme")
    print("=" * 80)
    print()
    
    # Files that exist in both themes but have different encodings
    encoding_mismatches = [
        {
            "file": "\\elements\\footer.html",
            "current": "ascii",
            "modified": "macroman",
            "issue": "MacRoman encoding instead of ASCII"
        },
        {
            "file": "\\elements\\snippets\\menu-mobile.html", 
            "current": "ascii",
            "modified": "iso-8859-1",
            "issue": "ISO-8859-1 instead of ASCII"
        },
        {
            "file": "\\elements\\snippets\\product-tags.html",
            "current": "ascii", 
            "modified": "iso-8859-1",
            "issue": "ISO-8859-1 instead of ASCII"
        },
        {
            "file": "\\elements\\snippets\\product.html",
            "current": "ascii",
            "modified": "iso-8859-1", 
            "issue": "ISO-8859-1 instead of ASCII"
        },
        {
            "file": "\\elements\\snippets\\title-showcase.html",
            "current": "ascii",
            "modified": "iso-8859-1",
            "issue": "ISO-8859-1 instead of ASCII"
        },
        {
            "file": "\\pages\\product.html",
            "current": "ascii",
            "modified": "iso-8859-1",
            "issue": "ISO-8859-1 instead of ASCII"
        }
    ]
    
    # Files that exist only in Modified theme with problematic encodings
    additional_problematic = [
        {
            "file": "\\elements\\footer-custom.html",
            "encoding": "utf-8",
            "issue": "UTF-8 encoding (should be ASCII for consistency)"
        },
        {
            "file": "\\elements\\footer-menus.html", 
            "encoding": "utf-8",
            "issue": "UTF-8 encoding (should be ASCII for consistency)"
        },
        {
            "file": "\\elements\\snippets\\fast-shopping-template.html",
            "encoding": "iso-8859-1",
            "issue": "ISO-8859-1 encoding (should be ASCII)"
        }
    ]
    
    # SCSS files detected as binary (potential encoding issues)
    scss_files_as_binary = [
        "\\css\\sass\\address.scss",
        "\\css\\sass\\banners.scss", 
        "\\css\\sass\\base.scss",
        "\\css\\sass\\breadcrumb.scss",
        "\\css\\sass\\cart.scss",
        "\\css\\sass\\customer.scss",
        "\\css\\sass\\footer.scss",
        "\\css\\sass\\header.scss",
        "\\css\\sass\\menu-mobile.scss",
        "\\css\\sass\\new-filter.scss",
        "\\css\\sass\\newsletter.scss",
        # ... and 30+ more SCSS files
    ]
    
    print("FILES WITH ENCODING MISMATCHES (Critical Issues):")
    print("-" * 60)
    for item in encoding_mismatches:
        print(f"FILE: {item['file']}")
        print(f"   SHOULD BE: {item['current']}")
        print(f"   CURRENTLY: {item['modified']}")
        print(f"   ISSUE: {item['issue']}")
        print()

    print("ADDITIONAL PROBLEMATIC FILES:")
    print("-" * 60)
    for item in additional_problematic:
        print(f"FILE: {item['file']}")
        print(f"   CURRENTLY: {item['encoding']}")
        print(f"   ISSUE: {item['issue']}")
        print()

    print("SCSS FILES DETECTED AS BINARY (Potential Issues):")
    print("-" * 60)
    print(f"   Total SCSS files: {len(scss_files_as_binary)}+ files")
    print("   Issue: SCSS files should be text, not binary")
    print("   Recommendation: Check for BOM or special characters")
    print()

    # Summary
    total_critical = len(encoding_mismatches)
    total_additional = len(additional_problematic)
    total_scss = len(scss_files_as_binary)

    print("=" * 80)
    print("SUMMARY:")
    print(f"   Critical encoding mismatches: {total_critical} files")
    print(f"   Additional problematic files: {total_additional} files")
    print(f"   SCSS files with potential issues: {total_scss}+ files")
    print(f"   Total files needing attention: {total_critical + total_additional} files")
    print("=" * 80)
    print()

    # Priority list for correction
    print("PRIORITY CORRECTION LIST:")
    print("-" * 60)
    print("HIGH PRIORITY (Encoding mismatches):")
    for i, item in enumerate(encoding_mismatches, 1):
        print(f"   {i}. {item['file']} ({item['modified']} → {item['current']})")
    
    print("\nMEDIUM PRIORITY (Additional issues):")
    for i, item in enumerate(additional_problematic, 1):
        print(f"   {i}. {item['file']} ({item['encoding']} → ascii)")
    
    print("\nLOW PRIORITY (SCSS files - investigate manually):")
    print("   • All files in \\css\\sass\\ folder")
    print()
    
    return {
        'critical': encoding_mismatches,
        'additional': additional_problematic,
        'scss_issues': scss_files_as_binary
    }

def generate_file_list_for_correction():
    """Generate a simple list of files that need encoding correction"""
    
    files_to_fix = [
        "Modified-codification-theme\\elements\\footer.html",
        "Modified-codification-theme\\elements\\snippets\\menu-mobile.html",
        "Modified-codification-theme\\elements\\snippets\\product-tags.html", 
        "Modified-codification-theme\\elements\\snippets\\product.html",
        "Modified-codification-theme\\elements\\snippets\\title-showcase.html",
        "Modified-codification-theme\\pages\\product.html",
        "Modified-codification-theme\\elements\\footer-custom.html",
        "Modified-codification-theme\\elements\\footer-menus.html",
        "Modified-codification-theme\\elements\\snippets\\fast-shopping-template.html"
    ]
    
    print("FILES TO FIX (Full Paths):")
    print("-" * 60)
    for i, file_path in enumerate(files_to_fix, 1):
        print(f"{i:2d}. {file_path}")
    
    return files_to_fix

if __name__ == "__main__":
    analysis_results = analyze_encoding_differences()
    print()
    files_list = generate_file_list_for_correction()
    
    # Save results to file
    with open("manual_encoding_correction_list.txt", "w", encoding="utf-8") as f:
        f.write("ENCODING CORRECTION LIST\n")
        f.write("=" * 50 + "\n\n")
        f.write("FILES NEEDING ENCODING CORRECTION:\n\n")
        
        f.write("HIGH PRIORITY (Encoding mismatches):\n")
        for item in analysis_results['critical']:
            f.write(f"• {item['file']} ({item['modified']} → {item['current']})\n")
        
        f.write("\nMEDIUM PRIORITY (Additional issues):\n")
        for item in analysis_results['additional']:
            f.write(f"• {item['file']} ({item['encoding']} → ascii)\n")
        
        f.write("\nLOW PRIORITY (SCSS files - investigate manually):\n")
        f.write("• All files in \\css\\sass\\ folder\n")
    
    print(f"\nResults saved to: manual_encoding_correction_list.txt")
