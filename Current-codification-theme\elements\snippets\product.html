{% set hover_class = product.available and (product.stock > 0 or settings.without_stock_sale) and not product.upon_request ? 'show-down' : '' %}

<div class="product {{ hover_class }}" {{ product.data_tray_ga4 }}>

    {% if pages.current == 'catalog' and settings.compare %}
        <div class="compare-buttons">

            <a href="{{ links.compare_delete ~ product.id }}" class="flex justify-center align-center {{ not product.compare ? 'compare-hidden' }}" data-compare="remove">
                <span class="filter-checkbox checked"></span>
                {{ Translation("remover_comparar") }}
            </a>

            <a href="{{ links.compare_add ~ product.id }}" class="flex justify-center align-center {{ product.compare ? 'compare-hidden' }}" data-compare="add">
                <span class="filter-checkbox"></span>
                {{ Translation("comparar_produto") }}
            </a>

        </div>
    {% endif %}

    <div class="image">

        {% set image_class = slide ? 'swiper-lazy' : 'lazyload' %}

        <a href="{{ product.link }}" class="space-image">
            <img class="{{ image_class }}" src="{{ asset('img/empty.png') }}" data-src="{{ product.images[0].large }}" alt="{{ product.name }}">
        </a>

        {% element 'snippets/product-tags' { 'product': product } %}

    </div>

    <a class="product-info" href="{{ product.link }}">

        <div class="product-name">
            {{ product.name }}
        </div>

        <div class="product-price">
            {% if product.available and (product.stock > 0 or settings.without_stock_sale) and not product.upon_request %}

                {% element 'snippets/product-price' { 'product': product } %}

            {% elseif product.upon_request %}

                {% element 'snippets/product-message' { 'product_message': 'Sob consulta', 'know_more' : 'Saiba mais' } %}

            {% elseif product.stock <= 0 %}

                {% element 'snippets/product-message' {  'product_message': 'Esse acabou :(', 'know_more' : 'Produto indispon&iacute;vel' } %}

            {% else %}

                {% element 'snippets/product-message' {  'product_message': 'Esse acabou :(', 'know_more' : 'Produto indispon&iacute;vel'  } %}

            {% endif %}
        </div>

    </a>

    <div class="actions">
        {% if product.available and (product.stock > 0 or settings.without_stock_sale) and not product.upon_request %}
            <a class="product-button" href="{{ product.link }}">
                {{ settings.bt_comprar_vitrine }}
            </a>
        {% endif %}
    </div>

</div>