.page-depoimentos{
    
    .page-content{
        font-size: 1.4rem;
        line-height: 1.6rem;
        
        > br{
            display: none;
        }
        
        > div{
            background: #fff;
            padding: 30px;
            
            &:last-child{
                margin-top: 0;
            }
            
            @media screen and (max-width: $sm){
                padding: 15px;
            }
        }
        
        .board{
            @media screen and (max-width: $sm){
                .left,
                .right{
                    float: none;
                    margin: 15px 0;
                }
            }
        }
        
        h1{
            font-size: 2rem;
            font-weight: 700;
        }
        
        h2{
            display: none;
        }
        
        input[type="text"]{
            display: block;
            margin-top: 3px;
            padding: 10px;
            width: 50%;
            
            @media screen and (max-width: $sm){
                width: 100%;
            }
        }
        
        textarea{
            margin-top: 3px;
            padding: 10px;
            width: 100% !important;
        }
        
        .btns-paginator {
            border: 1px solid #aaa;
            color: #aaa;
            display: inline-block;
            line-height: 2rem;
            margin: 0 5px;
            padding: 0 5px;
            vertical-align: top;
            
            img{
                display: none;    
            }
            
            a{
                color: #aaa;
                display: block;
                line-height: 2rem;
                padding: 0 5px;
            }
            
            &.selectedPg{
                border-color: #3d4445;
                color: #3d4445;
                padding: 0 5px;
            }
            
            @media screen and (max-width: $sm){
                &.btn-proxima-pagina,
                &.btn-ultima-pagina,
                &.btn-primeira-pagina,
                &.btn-pagina-anterior{
                    display: block !important;
                    margin: 15px 0 !important;
                    text-align: center;
                }
            }
        }
        
        .editDep{
            
            img{
                display: none;
            }
            
            li{
                border: none;
            }
            
            h3 {
                border-left: 13px solid #eee;
                margin: 5px;
                padding: 1px;
                padding-left: 5px;
            }
        }
        
        .separador-paginas{
            display: none;
        }
        
        #enviar_dep{
            background: url('../img/send-button.png') no-repeat left top;
            height: 0;
            padding: 20px 65px;
            width: 0;
        }
    }
}