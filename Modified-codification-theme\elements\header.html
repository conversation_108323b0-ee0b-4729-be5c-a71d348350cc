{% set whatsappNumber = Translation('ag_telefone_1') %}
{% set email = Translation('ag_email_1') %}
{% set phone = Translation('ag_telefone_2') %}
{% set phone3 = Translation('ag_telefone_3') %}

{% if settings.active_message %}
<div class="message-top">
    <div class="swiper-container">
        <div class="swiper-wrapper message-top__content">
            {% for i in 1..5 %}
                {% set link = attribute(settings, 'link_message_'~i) %}
                {% set title = attribute(settings, 'name_message_'~i) %}
                {% if title %}
                <div class="item swiper-slide">
                    <a href="{{ link ? link : 'javascript:;' }}" aria-label="{{ title }}">
                        {{ title }}
                    </a>
                </div>
                {% endif %}
                
            {% endfor %}
        </div>
        <div class="prev">
            <i class="icon icon-arrow-left"></i>
        </div>
        <div class="next">
            <i class="icon icon-arrow-right"></i>
        </div>
        <div class="dots"></div>
        
    </div>
</div>
{% endif %}

<div class="header-info">
    <div class="container">
        <div class="header-info__content flex align-end justify-end">
            <ul class="list flex"> 
            
                {% if whatsappNumber %}
                <li>
                    {% element 'snippets/whatsapp' { 'number': whatsappNumber, 'include_text' : false } %}
                </li>
                {% endif %}
                
                {% if phone %}
                {% set link = phone | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                <li>
                    <a href="tel:{{ link }}" title="Telefone: {{ phone }}">
                        <i class="icon icon-phone v-align-middle"></i>
                        {{ phone }}
                    </a>
                </li>
                {% endif %}
                
                {% if email %}
                <li>
                    <a href="mailto:{{ email }}" title="Email: {{ email }}">
                        <i class="icon icon-email v-align-middle"></i>
                        {{ email }}
                    </a>
                </li>
                {% endif %}

            </ul>
            <div class="social-media flex align-center">
                {% element 'snippets/social-media' %}
            </div>
        </div>
    </div>
</div>

<header class="header {{ settings.header_desktop_center ? 'header-center' : ''}} {{ settings.header_mobile_center ? 'header-center-mobile' : ''}}">
    <div class="bg header-desktop">
        <div class="line flex align-center justify-between container">
            <div class="header-menu" data-toggle="overlay-shadow" data-target="#menu-mobile">
                <div></div>
                <div></div>
                <div></div>
            </div>
             <a class="logo" href="{{ store.url }}" title="{{ store.name }}">
                {% if settings.logo %}
                    <img src="{{ asset(settings.logo) }}" alt="{{ store.name }}" width="222" height="36">
                {% endif %}

                {% if 'home' in pages.current %}
                    <h1 class="title-store">{{ store.name }}</h1>
                {% else %}
                    <div class="title-store">{{ store.name }}</div>
                {% endif %}
            </a> 
            <div class="search-move">
                {% element 'snippets/header-search' %}
            </div>
            <div class="header-right">
                <div class="contact flex align-center">

                    <i class="contact-icon icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-icon" viewBox="0 0 1024 1024" version="1.1" fill="currentColor"><path d="M512 947.106726c-239.915162 0-435.106726-195.191564-435.106726-435.106726S272.084838 76.893274 512 76.893274s435.106726 195.191564 435.106726 435.106726S751.915162 947.106726 512 947.106726zM512 111.019513c-221.1027 0-400.980487 179.877787-400.980487 400.980487s179.877787 400.980487 400.980487 400.980487 400.980487-179.877787 400.980487-400.980487S733.1027 111.019513 512 111.019513z"></path><path d="M481.170854 613.473089l-1.192152-15.524578c-3.577478-32.265868 7.178493-67.549465 37.060057-103.417371 26.888394-31.682583 41.829687-54.993498 41.829687-81.858356 0-30.513968-19.126616-50.808176-56.769958-51.392484-21.51092 0-45.430702 7.153934-60.370972 18.49319l-14.332427-37.619806c19.709901-14.356986 53.776787-23.944342 85.459371-23.944342 68.74264 0 99.816357 42.437531 99.816357 87.892793 0 40.636512-22.703071 69.934792-51.392484 104.001679-26.304086 31.049157-35.867906 57.378825-34.066887 87.843674l0.584308 15.524578L481.170854 613.472066zM468.030579 697.18056c0-22.143323 14.941294-37.667901 35.867906-37.667901 20.902052 0 35.259038 15.524578 35.259038 37.667901 0 20.926612-13.748119 37.035498-35.867906 37.035498C482.363006 734.216058 468.030579 718.107171 468.030579 697.18056z"></path></svg>
                    </i>
                    <div class="contact-text">
                        <span>Precisa de Ajuda?</span>
                        <strong>Atendimento</strong>
                    </div>
                    {% set link = whatsappNumber | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                    <div class="header-wrapper flex align-center">
                        <div class="header-wrapper__content flex flex-column">
                            {% if whatsappNumber %}
                            <div class="header-wrapper__item header-wrapper__item--whatsapp">
                                <a href="https://api.whatsapp.com/send?phone=55{{ link }}" title="WhatsApp: {{ whatsappNumber }}" target="_blank">
                                    <span><i class="icon icon-whatsapp v-align-middle"></i>Whatsapp:</span>
                                    {{ whatsappNumber }}
                                </a>
                            </div>
                            {% endif %}
                            
                            {% if phone %}
                                {% set link = phone | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                                <div class="header-wrapper__item header-wrapper__item--phone">
                                    <a href="tel:{{ link }}" title="Telefone: {{ phone }}">
                                        <span><i class="icon icon-phone v-align-middle"></i>Telefone:</span>
                                        {{ phone }}
                                    </a>
                                </div>
                            {% endif %}
                            
                            {% if phone3 %}
                                {% set link = phone3 | replace({'(': '', ')': '', '-': '', ' ': ''}) %}
                                <div class="header-wrapper__item header-wrapper__item--phone">
                                    <a href="tel:{{ link }}" title="Telefone: {{ phone }}">
                                        <span><i class="icon icon-phone v-align-middle"></i>Telefone:</span>
                                        {{ phone3 }}
                                    </a>
                                </div>
                            {% endif %}
                            
                            <div class="header-wrapper__item header-wrapper__item--email">
                                <a href="mailto:{{ email }}" title="Email: {{ email }}">
                                    <span><i class="icon icon-email v-align-middle"></i>E-mail:</span>
                                    {{ email }}
                                </a>
                            </div>
                            {% if not settings.shipping_truck_active %}
                            <div class="header-wrapper__item header-wrapper__item--truck">
                                <div class="header-wrapper__text">
                                    <span><i class="icon icon-truck"></i></span>
                                    Rastreie o seu pedido:
                                </div>
                                <div class="header-wrapper__form" url-shipping="{{ settings.shipping_truck_link ? settings.shipping_truck_link : 'https://rastreamentocorreios.info/consulta/' }}">
                                    <input type="text" placeholder="C&oacute;digo de rastreio">
                                    <div class="header-wrapper__button"><i class="icon icon-arrow-right"></i></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
    
                <div class="account flex align-center">
                    <a href="{{ links.login }}" title="Minha Conta">
                        <i class="account-icon icon">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 6 34 21.5" style="fill:none;"><path d="M26.1615 27.4789V24.9386C26.1615 23.5911 25.6263 22.2988 24.6734 21.346C23.7206 20.3932 22.4283 19.8579 21.0809 19.8579H10.9195C9.57201 19.8579 8.27972 20.3932 7.3269 21.346C6.37409 22.2988 5.83881 23.5911 5.83881 24.9386V27.4789" stroke="currentColor"></path><path d="M15.9999 16.6824C18.8059 16.6824 21.0806 14.4077 21.0806 11.6017C21.0806 8.79576 18.8059 6.52106 15.9999 6.52106C13.1939 6.52106 10.9192 8.79576 10.9192 11.6017C10.9192 14.4077 13.1939 16.6824 15.9999 16.6824Z" stroke="currentColor"></path></svg>
                        </i>
                    </a>
                   
                    <div class="account-text flex flex-column">
                        <span>Minha Conta</span>
                        <div class="login-links">
                            <a href="/loja/central_cliente-{{ store.id }}" title="Entrar">Acessar</a>
                        </div>
                    </div>

                    
			<script>
				window.__customer = '{{ customer | json_encode }}'
			</script>
                    
                    <div class="header-wrapper flex align-center">
                        <div class="header-wrapper__content flex flex-column">
                            <div class="tray-hide" data-logged-user="true">
                                <span>Ol&aacute;, <span data-customer="name"></span></span>
                                <div class="login-links flex flex-column">
                                    <a href="/loja/central_cliente-{{ store.id }}" title="Minha Conta" class="login-links__featured"><i class="icon icon-login v-align-middle"></i> Minha conta</a>
                                    <a href="{{ links.orders }}" title="Cadastrar">Meus pedidos</a>
                                    <a href="{{ links.logout }}" title="Sair">Sair</a>
                                </div>
                            </div>  
                            <div data-logged-user="false">
                                <div class="login-links flex flex-column">
                                    <a href="{{ links.login }}" title="Entrar" class="login-links__featured"><i class="icon icon-login-new v-align-middle"></i> Entrar</a>
                                    <a href="{{ links.orders }}" title="Cadastrar">Meus pedidos</a>
                                    <a href="{{ links.register }}" title="Cadastrar">Cadastrar</a>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
    
                <a class="cart-toggle" href="javascript:;" title="Carrinho" onclick="CarrinhoVue.closeDropdown()">
                    <i class="icon v-align-middle">
                        {% if settings.cart_icon_active %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg-icon" viewBox="0 0 1024 1024" version="1.1" style="opacity: 0.8;width: 35px;height: 32px;fill: currentColor;margin-bottom: -1px;"><path d="M955.423647 330.144162 852.08507 620.606557c-14.410198 40.402175-52.888558 67.575048-95.845928 67.575048L352.7843 688.181605c-48.409546 0-90.355891-34.474163-99.758028-81.935104l-68.38346-345.936847-22.624277-103.757109L86.263178 156.552545c-12.062733 0-21.815865-9.717316-21.815865-21.74014 0-12.06478 9.757225-21.775956 21.815865-21.775956l93.305059 0c0.681522 0 1.266853 0.344854 1.914606 0.38988 1.534959 0.141216 2.963495 0.417509 4.41659 0.87595 1.311879 0.417509 2.506077 0.848321 3.685949 1.502213 1.27504 0.652869 2.347464 1.416256 3.463891 2.300392 1.124614 0.88516 2.078335 1.77032 3.000334 2.845815 0.838088 1.039679 1.569752 2.152013 2.214435 3.345188 0.730641 1.294482 1.351787 2.609431 1.807159 4.025687 0.194428 0.653893 0.661056 1.161453 0.811482 1.842974l22.620184 103.833857 663.96097 0c23.387663 0 45.404096 11.373025 58.870806 30.463826 13.546527 19.09387 16.878412 43.642986 9.080819 65.701374l0 0L955.423647 330.144162zM910.746098 289.578258c-5.387707-7.647167-13.865799-12.063757-23.263843-12.063757L232.475214 277.514501l63.304791 320.290747c5.376451 27.122731 29.355585 46.796816 57.008388 46.796816L756.247328 644.602064c24.549116 0 46.561455-15.531741 54.78986-38.606273l103.334483-290.461371c3.118014-8.828063 1.842974-18.286482-3.617387-25.961278l0 0L910.746098 289.578258zM336.95273 735.414349c48.482201 0 87.895863 39.35431 87.895863 87.75874 0 48.409546-39.422871 87.790462-87.895863 87.790462-48.445362 0-87.853907-39.388079-87.853907-87.790462C249.099846 774.777869 288.504298 735.414349 336.95273 735.414349L336.95273 735.414349 336.95273 735.414349zM336.95273 867.434152c24.426319 0 44.291762-19.851117 44.291762-44.246737 0-24.376177-19.868513-44.200688-44.291762-44.200688-24.399713 0-44.246737 19.826558-44.246737 44.200688C292.705993 847.600431 312.562227 867.434152 336.95273 867.434152L336.95273 867.434152 336.95273 867.434152zM725.312782 735.414349c48.457642 0 87.862094 39.35431 87.862094 87.75874 0 48.409546-39.399335 87.790462-87.862094 87.790462-48.431036 0-87.835488-39.388079-87.835488-87.790462C637.477294 774.777869 676.890956 735.414349 725.312782 735.414349L725.312782 735.414349 725.312782 735.414349zM725.312782 867.434152c24.431435 0 44.296879-19.851117 44.296879-44.246737 0-24.376177-19.874653-44.200688-44.296879-44.200688-24.426319 0-44.274366 19.826558-44.274366 44.200688C681.033299 847.600431 700.881346 867.434152 725.312782 867.434152L725.312782 867.434152 725.312782 867.434152zM725.312782 867.434152"></path></svg>
                        {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" viewBox="0 0 17 20" fill="currentColor">
                                <path d="M0 20V4.995l1 .006v.015l4-.002V4c0-2.484 1.274-4 3.5-4C10.518 0 12 1.48 12 4v1.012l5-.003v.985H1V19h15V6.005h1V20H0zM11 4.49C11 2.267 10.507 1 8.5 1 6.5 1 6 2.27 6 4.49V5l5-.002V4.49z" fill="currentColor"></path>
                            </svg>
                        {% endif %}
                    </i>
                    <span class="cart-quantity" data-cart="amount">0</span>
                </a>
            </div>
        </div>
         {% element 'snippets/menu' %}
    </div>
    
    <div class="bg header-mobile {{settings.close_search_mobile ? 'search-closed': 'search-opened'}}">

        <div class="line flex align-center justify-between container">

            <div class="header-mobile__side-left">
                <div class="header-menu" data-toggle="overlay-shadow" data-target="#menu-mobile">
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            
            <div class="header-mobile__side-center">
                <a class="logo" href="{{ store.url }}" title="{{ store.name }}">
                    {% if settings.logo %}
                        <img src="{{ asset(settings.logo) }}" alt="{{ store.name }}" width="222" height="36">
                    {% endif %}
                </a> 
            </div>

            <div class="header-mobile__side-right">
                <div class="search-mobile flex align-center">
                    <svg xmlns="http://www.w3.org/2000/svg" role="presentation" viewBox="0 0 18 17">
                        <g transform="translate(1 1)" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square">
                            <path d="M16 16l-5.0752-5.0752"/>
                            <circle cx="6.4" cy="6.4" r="6.4"/>
                        </g>
                    </svg>
                </div>
                <div class="account flex align-center">
                    <a href="{{ links.login }}" title="Minha Conta">
                        <i class="account-icon icon">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 6 34 21.5" style="fill:none;"><path d="M26.1615 27.4789V24.9386C26.1615 23.5911 25.6263 22.2988 24.6734 21.346C23.7206 20.3932 22.4283 19.8579 21.0809 19.8579H10.9195C9.57201 19.8579 8.27972 20.3932 7.3269 21.346C6.37409 22.2988 5.83881 23.5911 5.83881 24.9386V27.4789" stroke="currentColor"></path><path d="M15.9999 16.6824C18.8059 16.6824 21.0806 14.4077 21.0806 11.6017C21.0806 8.79576 18.8059 6.52106 15.9999 6.52106C13.1939 6.52106 10.9192 8.79576 10.9192 11.6017C10.9192 14.4077 13.1939 16.6824 15.9999 16.6824Z" stroke="currentColor"></path></svg>
                        </i>
                    </a>
                </div>
    
                <a class="cart-toggle" href="javascript:;" title="Carrinho" onclick="CarrinhoVue.closeDropdown()">
                    <i class="icon v-align-middle">
                        {% if settings.cart_icon_active %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="svg-icon" viewBox="0 0 1024 1024" version="1.1" style="opacity: 0.8;width: 35px;height: 32px;fill: currentColor;margin-bottom: -1px;"><path d="M955.423647 330.144162 852.08507 620.606557c-14.410198 40.402175-52.888558 67.575048-95.845928 67.575048L352.7843 688.181605c-48.409546 0-90.355891-34.474163-99.758028-81.935104l-68.38346-345.936847-22.624277-103.757109L86.263178 156.552545c-12.062733 0-21.815865-9.717316-21.815865-21.74014 0-12.06478 9.757225-21.775956 21.815865-21.775956l93.305059 0c0.681522 0 1.266853 0.344854 1.914606 0.38988 1.534959 0.141216 2.963495 0.417509 4.41659 0.87595 1.311879 0.417509 2.506077 0.848321 3.685949 1.502213 1.27504 0.652869 2.347464 1.416256 3.463891 2.300392 1.124614 0.88516 2.078335 1.77032 3.000334 2.845815 0.838088 1.039679 1.569752 2.152013 2.214435 3.345188 0.730641 1.294482 1.351787 2.609431 1.807159 4.025687 0.194428 0.653893 0.661056 1.161453 0.811482 1.842974l22.620184 103.833857 663.96097 0c23.387663 0 45.404096 11.373025 58.870806 30.463826 13.546527 19.09387 16.878412 43.642986 9.080819 65.701374l0 0L955.423647 330.144162zM910.746098 289.578258c-5.387707-7.647167-13.865799-12.063757-23.263843-12.063757L232.475214 277.514501l63.304791 320.290747c5.376451 27.122731 29.355585 46.796816 57.008388 46.796816L756.247328 644.602064c24.549116 0 46.561455-15.531741 54.78986-38.606273l103.334483-290.461371c3.118014-8.828063 1.842974-18.286482-3.617387-25.961278l0 0L910.746098 289.578258zM336.95273 735.414349c48.482201 0 87.895863 39.35431 87.895863 87.75874 0 48.409546-39.422871 87.790462-87.895863 87.790462-48.445362 0-87.853907-39.388079-87.853907-87.790462C249.099846 774.777869 288.504298 735.414349 336.95273 735.414349L336.95273 735.414349 336.95273 735.414349zM336.95273 867.434152c24.426319 0 44.291762-19.851117 44.291762-44.246737 0-24.376177-19.868513-44.200688-44.291762-44.200688-24.399713 0-44.246737 19.826558-44.246737 44.200688C292.705993 847.600431 312.562227 867.434152 336.95273 867.434152L336.95273 867.434152 336.95273 867.434152zM725.312782 735.414349c48.457642 0 87.862094 39.35431 87.862094 87.75874 0 48.409546-39.399335 87.790462-87.862094 87.790462-48.431036 0-87.835488-39.388079-87.835488-87.790462C637.477294 774.777869 676.890956 735.414349 725.312782 735.414349L725.312782 735.414349 725.312782 735.414349zM725.312782 867.434152c24.431435 0 44.296879-19.851117 44.296879-44.246737 0-24.376177-19.874653-44.200688-44.296879-44.200688-24.426319 0-44.274366 19.826558-44.274366 44.200688C681.033299 847.600431 700.881346 867.434152 725.312782 867.434152L725.312782 867.434152 725.312782 867.434152zM725.312782 867.434152"></path></svg>
                        {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" role="presentation" aria-hidden="true" viewBox="0 0 17 20" fill="currentColor">
                                <path d="M0 20V4.995l1 .006v.015l4-.002V4c0-2.484 1.274-4 3.5-4C10.518 0 12 1.48 12 4v1.012l5-.003v.985H1V19h15V6.005h1V20H0zM11 4.49C11 2.267 10.507 1 8.5 1 6.5 1 6 2.27 6 4.49V5l5-.002V4.49z" fill="currentColor"></path>
                            </svg>
                        {% endif %}
                    </i>
                    <span class="cart-quantity" data-cart="amount">0</span>
                </a>
            </div>
            
            {% element 'snippets/header-search' %}
        </div>
        {% element 'snippets/menu' %}
    </div>


</header>