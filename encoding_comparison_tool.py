#!/usr/bin/env python3
"""
Encoding Comparison Too<PERSON>
Compares encoding analysis between Current-codification-theme and Modified-codification-theme
to identify files that need correction.
"""

import re
from pathlib import Path
from typing import Dict, Set, List, Tuple


class EncodingComparator:
    def __init__(self, current_report: str, modified_report: str):
        self.current_encodings = self.parse_report(current_report)
        self.modified_encodings = self.parse_report(modified_report)
        
    def parse_report(self, report_path: str) -> Dict[str, str]:
        """Parse encoding report file and return dict of file_path -> encoding"""
        encodings = {}
        
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Extract file entries using regex
            # Pattern matches: "   filename  (encoding)"
            pattern = r'^\s+([^\s]+)\s+\(([^)]+)\)$'
            current_folder = ""
            
            for line in content.split('\n'):
                line = line.strip()
                
                # Check if it's a folder line (starts with \)
                if line.startswith('\\'):
                    current_folder = line
                    continue
                    
                # Check if it's a file entry
                match = re.match(pattern, line)
                if match:
                    filename = match.group(1)
                    encoding = match.group(2)
                    
                    # Create full path
                    if current_folder == '\\':
                        full_path = filename
                    else:
                        full_path = f"{current_folder}\\{filename}"
                    
                    encodings[full_path] = encoding
                    
        except Exception as e:
            print(f"Error parsing {report_path}: {e}")
            
        return encodings
    
    def find_encoding_mismatches(self) -> List[Tuple[str, str, str]]:
        """Find files with different encodings between current and modified themes"""
        mismatches = []
        
        # Find common files between both themes
        current_files = set(self.current_encodings.keys())
        modified_files = set(self.modified_encodings.keys())
        common_files = current_files.intersection(modified_files)
        
        for file_path in sorted(common_files):
            current_encoding = self.current_encodings[file_path]
            modified_encoding = self.modified_encodings[file_path]
            
            if current_encoding != modified_encoding:
                mismatches.append((file_path, current_encoding, modified_encoding))
                
        return mismatches
    
    def find_problematic_files(self) -> List[Tuple[str, str]]:
        """Find files in modified theme with problematic encodings"""
        problematic = []
        problematic_encodings = {
            'iso-8859-1', 'macroman', 'utf-8', 'binary'
        }
        
        for file_path, encoding in self.modified_encodings.items():
            # Skip binary files that should be binary (images, fonts, etc.)
            if encoding == 'binary':
                file_ext = Path(file_path).suffix.lower()
                text_extensions = {'.html', '.css', '.js', '.json', '.svg', '.scss'}
                if file_ext in text_extensions:
                    problematic.append((file_path, encoding))
            elif encoding in {'iso-8859-1', 'macroman'}:
                problematic.append((file_path, encoding))
            elif 'confidence:' in encoding and 'utf-8' not in encoding.lower():
                problematic.append((file_path, encoding))
                
        return problematic
    
    def generate_correction_report(self) -> str:
        """Generate a comprehensive correction report"""
        report = []
        report.append("=" * 80)
        report.append("ENCODING CORRECTION REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Find mismatches
        mismatches = self.find_encoding_mismatches()
        if mismatches:
            report.append("FILES WITH ENCODING MISMATCHES (need correction):")
            report.append("-" * 60)
            for file_path, current_enc, modified_enc in mismatches:
                report.append(f"📁 {file_path}")
                report.append(f"   ✅ Current (correct): {current_enc}")
                report.append(f"   ❌ Modified (wrong):  {modified_enc}")
                report.append("")
        
        # Find problematic files
        problematic = self.find_problematic_files()
        if problematic:
            report.append("ADDITIONAL PROBLEMATIC FILES:")
            report.append("-" * 60)
            for file_path, encoding in problematic:
                if file_path not in [m[0] for m in mismatches]:  # Don't duplicate
                    report.append(f"📁 {file_path}")
                    report.append(f"   ❌ Current encoding: {encoding}")
                    report.append(f"   ✅ Should be: ascii (or utf-8)")
                    report.append("")
        
        # Summary
        total_issues = len(mismatches) + len([p for p in problematic if p[0] not in [m[0] for m in mismatches]])
        report.append("=" * 80)
        report.append("SUMMARY:")
        report.append(f"• Files with encoding mismatches: {len(mismatches)}")
        report.append(f"• Additional problematic files: {len(problematic) - len(mismatches)}")
        report.append(f"• Total files needing correction: {total_issues}")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def get_files_to_fix(self) -> List[str]:
        """Get list of file paths that need encoding correction"""
        files_to_fix = []
        
        # Add mismatched files
        mismatches = self.find_encoding_mismatches()
        for file_path, _, _ in mismatches:
            files_to_fix.append(file_path)
        
        # Add problematic files not already in mismatches
        problematic = self.find_problematic_files()
        mismatch_files = {m[0] for m in mismatches}
        
        for file_path, _ in problematic:
            if file_path not in mismatch_files:
                files_to_fix.append(file_path)
        
        return sorted(files_to_fix)


def main():
    # File paths
    current_report = "encoding_analysis_report.txt"
    modified_report = "modified_theme_encoding_analysis.txt"
    
    # Check if files exist
    if not Path(current_report).exists():
        print(f"Error: {current_report} not found!")
        return
    if not Path(modified_report).exists():
        print(f"Error: {modified_report} not found!")
        return
    
    # Create comparator
    comparator = EncodingComparator(current_report, modified_report)
    
    # Generate and display report
    correction_report = comparator.generate_correction_report()
    print(correction_report)
    
    # Save report to file
    with open("encoding_correction_report.txt", "w", encoding="utf-8") as f:
        f.write(correction_report)
    
    print(f"\nDetailed report saved to: encoding_correction_report.txt")
    
    # Generate file list for correction script
    files_to_fix = comparator.get_files_to_fix()
    if files_to_fix:
        print(f"\nFiles that need correction ({len(files_to_fix)} total):")
        for file_path in files_to_fix:
            print(f"  • {file_path}")


if __name__ == "__main__":
    main()
