#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para decodificar entidades HTML em arquivos de texto.

Este script pode ser usado para converter entidades HTML como:
&ccedil; -> ç
&atilde; -> ã
&aacute; -> á
&eacute; -> é
&iacute; -> í
&ocirc; -> ô
&uacute; -> ú

Uso:
    python decodificar_html.py arquivo_entrada.html arquivo_saida.html
    ou
    python decodificar_html.py arquivo_entrada.html  # sobrescreve o arquivo original
"""

import html
import sys
import os
import argparse
import chardet
from pathlib import Path


def decodificar_entidades_html(texto):
    """
    Decodifica entidades HTML em um texto.
    
    Args:
        texto (str): Texto contendo entidades HTML
        
    Returns:
        str: Texto com entidades HTML decodificadas
    """
    return html.unescape(texto)


def processar_arquivo(arquivo_entrada, arquivo_saida=None):
    """
    Processa um arquivo decodificando entidades HTML.
    
    Args:
        arquivo_entrada (str): Caminho do arquivo de entrada
        arquivo_saida (str, optional): Caminho do arquivo de saída. 
                                     Se None, sobrescreve o arquivo original.
    """
    try:
        # Detecta a codificação do arquivo
        with open(arquivo_entrada, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding']

        # Lê o arquivo original
        with open(arquivo_entrada, 'r', encoding=encoding) as f:
            conteudo_original = f.read()
        
        # Decodifica as entidades HTML
        conteudo_decodificado = decodificar_entidades_html(conteudo_original)
        
        # Define o arquivo de saída
        if arquivo_saida is None:
            arquivo_saida = arquivo_entrada
        
        # Escreve o arquivo decodificado
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            f.write(conteudo_decodificado)
        
        print(f"Arquivo processado com sucesso!")
        print(f"Entrada: {arquivo_entrada}")
        print(f"Saida: {arquivo_saida}")

        # Mostra algumas estatísticas
        entidades_antes = conteudo_original.count('&')
        entidades_depois = conteudo_decodificado.count('&')
        print(f"Entidades HTML antes: {entidades_antes}")
        print(f"Entidades HTML depois: {entidades_depois}")
        print(f"Entidades decodificadas: {entidades_antes - entidades_depois}")

    except FileNotFoundError:
        print(f"ERRO: Arquivo '{arquivo_entrada}' nao encontrado.")
        sys.exit(1)
    except Exception as e:
        print(f"ERRO ao processar arquivo: {e}")
        sys.exit(1)


def processar_texto_interativo():
    """
    Permite ao usuário inserir texto diretamente para decodificação.
    """
    print("Modo interativo - Digite o texto com entidades HTML:")
    print("(Digite 'sair' para terminar)")

    while True:
        texto = input("\n> ")
        if texto.lower() == 'sair':
            break

        texto_decodificado = decodificar_entidades_html(texto)
        print(f"Resultado: {texto_decodificado}")


def main():
    parser = argparse.ArgumentParser(
        description="Decodifica entidades HTML em arquivos de texto",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python decodificar_html.py settings.html                    # Sobrescreve o arquivo original
  python decodificar_html.py settings.html settings_novo.html # Cria novo arquivo
  python decodificar_html.py --interativo                     # Modo interativo
  
Entidades HTML comuns que serao decodificadas:
  &ccedil; -> c    &atilde; -> a    &aacute; -> a
  &eacute; -> e    &iacute; -> i    &ocirc; -> o
  &uacute; -> u    &ntilde; -> n    &amp; -> &
  &lt; -> <        &gt; -> >        &quot; -> "
        """
    )
    
    parser.add_argument(
        'arquivo_entrada', 
        nargs='?',
        help='Arquivo de entrada contendo entidades HTML'
    )
    
    parser.add_argument(
        'arquivo_saida', 
        nargs='?',
        help='Arquivo de saída (opcional - se não especificado, sobrescreve o original)'
    )
    
    parser.add_argument(
        '--interativo', '-i',
        action='store_true',
        help='Modo interativo para decodificar texto diretamente'
    )
    
    args = parser.parse_args()
    
    if args.interativo:
        processar_texto_interativo()
    elif args.arquivo_entrada:
        if not os.path.exists(args.arquivo_entrada):
            print(f"ERRO: Arquivo '{args.arquivo_entrada}' nao encontrado.")
            sys.exit(1)

        processar_arquivo(args.arquivo_entrada, args.arquivo_saida)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
