#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar se a decodificação foi bem-sucedida
"""

import chardet

def verificar_arquivo(arquivo):
    """
    Verifica a codificação e conteúdo do arquivo
    """
    print(f"=== Verificando: {arquivo} ===")
    
    # Detecta codificação
    with open(arquivo, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        confidence = result['confidence']
    
    print(f"Codificacao: {encoding} (confianca: {confidence:.2%})")
    
    # Lê o arquivo
    with open(arquivo, 'r', encoding=encoding) as f:
        conteudo = f.read()
    
    # Verifica exemplos específicos
    exemplos = [
        "Configurações",
        "Título", 
        "você não",
        "Atenção",
        "Seção",
        "Eletrônicos",
        "específica",
        "rodapé",
        "cronômetros"
    ]
    
    print("Palavras encontradas:")
    encontradas = 0
    for exemplo in exemplos:
        if exemplo in conteudo:
            print(f"  OK: {exemplo}")
            encontradas += 1
        else:
            print(f"  NAO: {exemplo}")
    
    # Conta entidades HTML restantes
    entidades_restantes = conteudo.count('&')
    print(f"\nEntidades HTML restantes: {entidades_restantes}")
    
    # Verifica se ainda há entidades comuns
    entidades_comuns = [
        "&ccedil;", "&atilde;", "&aacute;", "&eacute;", 
        "&iacute;", "&ocirc;", "&uacute;", "&ntilde;"
    ]
    
    entidades_encontradas = []
    for entidade in entidades_comuns:
        if entidade in conteudo:
            entidades_encontradas.append(entidade)
    
    if entidades_encontradas:
        print(f"AVISO: Ainda existem entidades nao decodificadas: {entidades_encontradas}")
    else:
        print("OK: Todas as entidades comuns foram decodificadas")
    
    print(f"Taxa de sucesso: {encontradas}/{len(exemplos)} palavras encontradas")
    print()
    
    return {
        'arquivo': arquivo,
        'encoding': encoding,
        'palavras_encontradas': encontradas,
        'total_palavras': len(exemplos),
        'entidades_restantes': entidades_restantes,
        'entidades_nao_decodificadas': entidades_encontradas
    }

def main():
    print("=== VERIFICADOR DE RESULTADOS ===\n")
    
    arquivos = [
        "settings.html",           # Original
        "settings_final.html"      # Resultado final
    ]
    
    resultados = []
    for arquivo in arquivos:
        try:
            resultado = verificar_arquivo(arquivo)
            resultados.append(resultado)
        except FileNotFoundError:
            print(f"ERRO: Arquivo {arquivo} nao encontrado\n")
        except Exception as e:
            print(f"ERRO ao verificar {arquivo}: {e}\n")
    
    # Resumo comparativo
    if len(resultados) >= 2:
        print("=== RESUMO COMPARATIVO ===")
        original = resultados[0]
        final = resultados[1]
        
        print(f"Arquivo original: {original['encoding']}")
        print(f"Arquivo final: {final['encoding']}")
        print(f"Entidades decodificadas: {original['entidades_restantes'] - final['entidades_restantes']}")
        print(f"Palavras corretas no resultado: {final['palavras_encontradas']}/{final['total_palavras']}")
        
        if final['entidades_nao_decodificadas']:
            print(f"ATENCAO: Ainda restam entidades: {final['entidades_nao_decodificadas']}")
        else:
            print("SUCESSO: Todas as entidades foram decodificadas!")

if __name__ == "__main__":
    main()
